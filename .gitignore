######################################################################
# Build Tools

.gradle
/build/
!gradle/wrapper/gradle-wrapper.jar

target/
!.mvn/wrapper/maven-wrapper.jar

######################################################################
# IDE

### STS ###
.apt_generated
.classpath
.factorypath
.project
.settings
.springBeans

### IntelliJ IDEA ###
.idea
*.iws
*.iml
*.ipr

### JRebel ###
rebel.xml

### NetBeans ###
nbproject/private/
build/*
nbbuild/
dist/
nbdist/
.nb-gradle/

### VS Code ###
.vscode/

######################################################################
# Node.js & npm
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.npm
.eslintcache

# Vue.js build outputs
/ruoyi-ui/dist/
/ruoyi-ui/.tmp/
/ruoyi-ui/.cache/

######################################################################
# MacOS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

######################################################################
# Application specific
*.jar
!**/src/main/**/target/
!**/src/test/**/target/

# Log files
*.log
logs/
app.log

# Temporary files
*.tmp
*.temp
*.swp
*.bak

# Upload files
uploadPath/
uploadExcel/

# Database dump files
*.sql
Dump*/

# Environment and config
.env
.env.local
.env.*.local

# Claude AI
.claude/

######################################################################
# Others
*.xml.versionsBackup

!*/build/*.java
!*/build/*.html
!*/build/*.xml
