# AuditTrain 审计实训教学系统

## 项目简介

AuditTrain是一个基于RuoYi-Vue框架开发的审计实训教学系统，专门为审计专业学生提供实践训练平台。系统集成了课程管理、实训练习、智能问答等功能，支持Excel数据处理和AI辅助学习。

## 技术栈

### 后端技术
- **框架**: Spring Boot 2.5.15
- **数据库**: MySQL 5.7
- **缓存**: Redis 6.0
- **权限**: Spring Security + JWT
- **持久层**: MyBatis
- **连接池**: Druid
- **构建工具**: Maven 3.x

### 前端技术
- **框架**: Vue 2.6.12
- **UI库**: Element UI 2.15.14
- **HTTP客户端**: Axios
- **Excel处理**: Luckysheet, ExcelJS
- **图表**: ECharts
- **构建工具**: Vue CLI 4.x

### 开发环境
- **Java**: JDK 1.8+
- **Node.js**: 14.x+
- **容器**: Docker & Docker Compose

## 功能特性

### 🎯 核心功能
- **用户管理**: 学生、教师账号管理
- **课程管理**: 审计课程创建、编辑、发布
- **实训练习**: 交互式审计实训模块
- **Excel处理**: 在线Excel编辑和数据分析
- **智能问答**: 集成OpenAI API的AI助手
- **成绩管理**: 学习进度跟踪和成绩统计

### 🛠 系统功能
- **权限管理**: 基于角色的访问控制
- **系统监控**: 性能监控和日志管理
- **代码生成**: 自动生成CRUD代码
- **定时任务**: Quartz调度器
- **文件管理**: 上传下载和分类管理

## 快速开始

### 环境要求

#### Windows开发环境
```
- Windows 10/11
- JDK 1.8+
- Maven 3.6+
- Node.js 14.x+
- MySQL 5.7+
- Redis 6.0+
- Git
```

### 安装步骤

#### 1. 克隆项目
```bash
git clone https://github.com/luQingU/AuditTrain.git
cd AuditTrain
```

#### 2. 数据库配置

**安装MySQL**
```bash
# 下载并安装MySQL 5.7
# 创建数据库
CREATE DATABASE auditTrain DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

**导入数据库结构**
```bash
# 进入sql目录，执行初始化脚本
mysql -u root -p auditTrain < sql/quartz.sql
mysql -u root -p auditTrain < sql/ry_20241101.sql
```

**配置数据库连接**
```yaml
# 修改 ruoyi-admin/src/main/resources/application-druid.yml
spring:
  datasource:
    master:
      url: ****************************************************************************************************************************************************
      username: root
      password: 你的密码
```

#### 3. Redis配置

**安装Redis**
```bash
# Windows: 下载Redis for Windows
# 或使用Docker
docker run -d --name redis -p 6379:6379 redis:6.0-alpine
```

#### 4. 后端启动

```bash
# 进入后端目录
cd ruoyi-admin

# 安装依赖
mvn clean install

# 启动应用
mvn spring-boot:run
```

**验证后端**
- 访问: http://localhost:8080
- Swagger文档: http://localhost:8080/swagger-ui/index.html

#### 5. 前端启动

```bash
# 进入前端目录
cd ruoyi-ui

# 安装依赖
npm install

# 启动开发服务器
npm run dev
```

**验证前端**
- 访问: http://localhost:80
- 默认账号: admin/admin123

### Docker部署（推荐）

项目已配置Docker Compose，一键启动所有服务：

```bash
# 启动数据库服务
docker-compose up -d mysql redis

# 等待数据库初始化完成（约30秒）
# 然后启动应用（需要单独构建）
```

### 生产环境部署

#### 1. 构建项目

**后端打包**
```bash
cd ruoyi-admin
mvn clean package -P prod
```

**前端打包**
```bash
cd ruoyi-ui
npm run build:prod
```

#### 2. 部署配置

**Nginx配置**
```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    location / {
        root /path/to/ruoyi-ui/dist;
        try_files $uri $uri/ /index.html;
    }
    
    location /prod-api/ {
        proxy_pass http://localhost:8080/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

**系统服务配置**
```bash
# 创建systemd服务文件
sudo vim /etc/systemd/system/audittrain.service

[Unit]
Description=AuditTrain Service
After=network.target

[Service]
Type=forking
ExecStart=/usr/bin/java -jar /path/to/ruoyi-admin.jar
User=audittrain
Group=audittrain
Restart=always

[Install]
WantedBy=multi-user.target
```

## 开发指南

### 目录结构
```
AuditTrain/
├── ruoyi-admin/          # 后端主应用
├── ruoyi-common/         # 通用工具类
├── ruoyi-framework/      # 核心框架
├── ruoyi-generator/      # 代码生成器
├── ruoyi-quartz/         # 定时任务
├── ruoyi-system/         # 系统模块
├── ruoyi-ui/            # 前端Vue应用
├── sql/                 # 数据库脚本
├── docker-compose.yml   # Docker配置
└── README.md           # 项目文档
```

### 核心配置文件
- `ruoyi-admin/src/main/resources/application.yml` - 主配置
- `ruoyi-admin/src/main/resources/application-druid.yml` - 数据源配置
- `ruoyi-ui/vue.config.js` - 前端构建配置
- `ruoyi-ui/.env.development` - 前端开发环境

### 常用开发命令

**后端开发**
```bash
# 编译
mvn compile

# 运行测试
mvn test

# 打包
mvn package

# 跳过测试打包
mvn package -DskipTests
```

**前端开发**
```bash
# 安装依赖
npm install

# 开发模式
npm run dev

# 构建生产版本
npm run build:prod

# 代码检查
npm run lint
```

## 功能模块详解

### 1. 审计课程模块
- 课程创建和管理
- 课件上传和组织
- 学习进度跟踪
- 实训案例库

### 2. Excel实训模块
- 在线Excel编辑器
- 审计底稿模板
- 数据验证和计算
- 导入导出功能

### 3. AI问答助手
- 集成OpenAI GPT模型
- 审计专业知识问答
- 实时对话支持
- 学习建议生成

### 4. 用户权限管理
- 学生/教师角色区分
- 细粒度权限控制
- 部门组织架构
- 操作日志记录

## 常见问题

### 启动问题

**Q: 后端启动失败，提示数据库连接错误**
A: 检查MySQL服务是否启动，数据库配置是否正确

**Q: 前端启动失败，提示端口被占用**
A: 修改vue.config.js中的端口配置或关闭占用端口的进程

**Q: Redis连接失败**
A: 确保Redis服务正在运行，检查端口6379是否可访问

### 功能问题

**Q: Excel编辑器无法正常加载**
A: 检查网络连接，确保CDN资源可正常访问

**Q: AI问答功能不可用**
A: 检查OpenAI API Key配置，确保账户额度充足

**Q: 文件上传失败**
A: 检查uploadPath目录权限，确保应用有写入权限

### 部署问题

**Q: 生产环境静态资源404**
A: 检查Nginx配置，确保静态资源路径正确

**Q: 跨域请求被拦截**
A: 配置正确的CORS设置或使用代理

## 更新日志

### v1.0.0 (2025-01-15)
- 初始版本发布
- 基础用户管理功能
- 课程管理模块
- Excel在线编辑
- AI问答集成

## 技术支持

### 开发团队
- 项目负责人: [您的姓名]
- 技术支持: [联系方式]

### 相关链接
- [项目地址](https://github.com/luQingU/AuditTrain)
- [问题反馈](https://github.com/luQingU/AuditTrain/issues)
- [RuoYi框架](https://gitee.com/y_project/RuoYi-Vue)

### 贡献指南
欢迎提交Issue和Pull Request来改善项目。

### 许可证
本项目采用MIT许可证，详见[LICENSE](LICENSE)文件。

---

**⚠️ 重要提醒**
- 生产环境请及时更新默认密码
- 定期备份数据库数据
- 监控系统资源使用情况
- 保持依赖版本更新