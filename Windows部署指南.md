# AuditTrain Windows部署指南

## 🚀 快速开始

本文档专为Windows用户提供详细的部署说明，确保您能够顺利运行AuditTrain审计实训教学系统。

## 📋 环境准备

### 必需软件安装

#### 1. Java开发环境
```bash
# 下载并安装 Oracle JDK 8 或 OpenJDK 8
# 推荐下载地址：
# Oracle: https://www.oracle.com/java/technologies/javase/javase8-archive-downloads.html
# OpenJDK: https://adoptopenjdk.net/

# 验证安装
java -version
javac -version
```

**环境变量配置：**
1. 右键"此电脑" → "属性" → "高级系统设置"
2. 点击"环境变量"
3. 新建系统变量：
   - `JAVA_HOME`: `C:\Program Files\Java\jdk1.8.0_xxx`
   - 编辑`Path`，添加：`%JAVA_HOME%\bin`

#### 2. Maven构建工具
```bash
# 下载Maven 3.6+
# 下载地址：https://maven.apache.org/download.cgi

# 解压到 C:\Program Files\Apache\maven

# 环境变量配置
MAVEN_HOME: C:\Program Files\Apache\maven
Path添加: %MAVEN_HOME%\bin

# 验证安装
mvn -version
```

#### 3. Node.js环境
```bash
# 下载Node.js 14.x LTS版本
# 下载地址：https://nodejs.org/

# 安装后验证
node -v
npm -v

# 配置npm国内镜像（可选）
npm config set registry https://registry.npm.taobao.org
```

#### 4. MySQL数据库
```bash
# 下载MySQL 5.7
# 下载地址：https://dev.mysql.com/downloads/mysql/5.7.html

# 选择MySQL Installer for Windows
# 选择Custom安装类型，选择MySQL Server 5.7
```

**MySQL配置：**
1. 安装时设置root密码（建议：123456）
2. 选择端口：3306
3. 字符集：utf8mb4

#### 5. Redis缓存
```bash
# Windows版Redis下载
# 下载地址：https://github.com/microsoftarchive/redis/releases

# 或者使用WSL/Docker安装
docker run -d --name redis -p 6379:6379 redis:6.0-alpine
```

#### 6. Git版本控制
```bash
# 下载Git for Windows
# 下载地址：https://git-scm.com/download/win

# 验证安装
git --version
```

## 💾 数据库初始化

### 创建数据库
1. 打开MySQL命令行客户端或使用Navicat等工具
2. 执行以下SQL命令：

```sql
-- 创建数据库
CREATE DATABASE auditTrain DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 使用数据库
USE auditTrain;
```

### 导入初始数据
1. 进入项目sql目录
2. 依次执行SQL文件：

```bash
# 使用命令行导入
cd AuditTrain\sql
mysql -u root -p123456 auditTrain < quartz.sql
mysql -u root -p123456 auditTrain < ry_20241101.sql

# 或者使用MySQL Workbench等图形化工具导入
```

## 🛠 项目部署

### 1. 下载项目代码
```bash
# 使用Git克隆项目
git clone https://github.com/luQingU/AuditTrain.git
cd AuditTrain
```

### 2. 配置数据库连接
编辑文件：`ruoyi-admin\src\main\resources\application-druid.yml`

```yaml
spring:
    datasource:
        type: com.alibaba.druid.pool.DruidDataSource
        driverClassName: com.mysql.cj.jdbc.Driver
        druid:
            master:
                url: *********************************************************************************************************************************************************************************
                username: root
                password: 123456  # 修改为您的MySQL密码
```

### 3. 配置Redis连接
编辑文件：`ruoyi-admin\src\main\resources\application.yml`

```yaml
spring:
  redis:
    host: localhost
    port: 6379
    password:  # 如果设置了密码请填写
    database: 0
```

### 4. 启动后端服务
```bash
# 进入后端目录
cd ruoyi-admin

# 清理并安装依赖
mvn clean install

# 启动应用（开发模式）
mvn spring-boot:run

# 或者先打包再运行
mvn clean package -DskipTests
java -jar target\ruoyi-admin.jar
```

**验证后端启动：**
- 浏览器访问：http://localhost:8080
- 看到欢迎页面说明启动成功

### 5. 启动前端服务
```bash
# 打开新的命令行窗口
# 进入前端目录
cd ruoyi-ui

# 安装依赖（首次运行需要）
npm install

# 启动开发服务器
npm run dev
```

**验证前端启动：**
- 浏览器访问：http://localhost:80
- 默认账号密码：admin / admin123

## 🔧 常见问题解决

### Java相关问题

**Q: 提示找不到java命令**
```bash
# 检查Java安装
java -version

# 如果报错，检查环境变量配置
echo %JAVA_HOME%
echo %PATH%
```

**Q: Maven下载依赖缓慢**
```xml
<!-- 配置阿里云Maven仓库 -->
<!-- 编辑 C:\Users\<USER>\.m2\settings.xml -->
<mirrors>
    <mirror>
        <id>alimaven</id>
        <name>aliyun maven</name>
        <url>https://maven.aliyun.com/repository/public</url>
        <mirrorOf>central</mirrorOf>
    </mirror>
</mirrors>
```

### 数据库相关问题

**Q: 数据库连接失败**
```bash
# 检查MySQL服务状态
# 按Win+R，输入services.msc，查找MySQL服务

# 检查端口是否被占用
netstat -an | findstr 3306

# 测试数据库连接
mysql -u root -p -h localhost -P 3306
```

**Q: 字符集问题导致中文乱码**
```sql
-- 检查数据库字符集
SHOW VARIABLES LIKE 'character%';

-- 修改数据库字符集
ALTER DATABASE auditTrain CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

### Node.js相关问题

**Q: npm install失败**
```bash
# 清理npm缓存
npm cache clean --force

# 使用国内镜像
npm install --registry https://registry.npm.taobao.org

# 如果还是失败，删除node_modules重新安装
rmdir /s node_modules
npm install
```

**Q: 端口冲突**
```bash
# 查看端口占用
netstat -ano | findstr :80

# 杀死占用端口的进程
taskkill /PID {进程ID} /F

# 或修改vue.config.js中的端口配置
```

### Redis相关问题

**Q: Redis连接失败**
```bash
# 检查Redis是否启动
# 如果使用Redis for Windows
redis-server.exe

# 如果使用Docker
docker ps | findstr redis
docker start redis
```

## 📦 生产环境部署

### 1. 项目打包
```bash
# 后端打包
cd ruoyi-admin
mvn clean package -P prod -DskipTests

# 前端打包
cd ruoyi-ui
npm run build:prod
```

### 2. Windows服务配置
使用NSSM将Java应用注册为Windows服务：

```bash
# 下载NSSM: https://nssm.cc/download
# 安装服务
nssm install AuditTrain "C:\Program Files\Java\jdk1.8.0_xxx\bin\java.exe"
nssm set AuditTrain Arguments "-jar C:\path\to\ruoyi-admin.jar"
nssm set AuditTrain AppDirectory "C:\path\to\project"

# 启动服务
nssm start AuditTrain
```

### 3. IIS部署前端
1. 启用IIS功能
2. 将dist文件夹内容复制到IIS站点目录
3. 配置URL重写规则（需要安装URL Rewrite模块）

```xml
<!-- web.config -->
<configuration>
  <system.webServer>
    <rewrite>
      <rules>
        <rule name="Handle History Mode and hash mode" stopProcessing="true">
          <match url="(.*)" />
          <conditions logicalGrouping="MatchAll">
            <add input="{REQUEST_FILENAME}" matchType="IsFile" negate="true" />
            <add input="{REQUEST_FILENAME}" matchType="IsDirectory" negate="true" />
          </conditions>
          <action type="Rewrite" url="/" />
        </rule>
      </rules>
    </rewrite>
  </system.webServer>
</configuration>
```

## 📝 开发建议

### IDE推荐
- **后端开发**: IntelliJ IDEA Community Edition
- **前端开发**: Visual Studio Code
- **数据库管理**: Navicat Premium 或 MySQL Workbench

### 开发插件推荐

**IntelliJ IDEA插件：**
- Maven Helper
- Lombok
- MyBatis Log Plugin
- RestfulToolkit

**VSCode插件：**
- Vue Language Features (Volar)
- Vue 2 Snippets
- Element UI Snippets
- Prettier - Code formatter

### 开发环境配置
```bash
# 配置Git用户信息
git config --global user.name "您的姓名"
git config --global user.email "您的邮箱"

# 设置行尾符（Windows推荐）
git config --global core.autocrlf true
```

## 🚨 安全注意事项

1. **数据库安全**
   - 修改默认密码
   - 限制数据库访问IP
   - 定期备份数据

2. **应用安全**
   - 更换JWT密钥
   - 配置HTTPS
   - 设置防火墙规则

3. **系统安全**
   - 及时更新系统补丁
   - 使用杀毒软件
   - 定期检查日志

## 📞 技术支持

如果在部署过程中遇到问题，请：
1. 查看控制台日志输出
2. 检查应用日志文件
3. 在GitHub项目中提交Issue

---

**祝您部署顺利！如有问题，欢迎反馈。**