services:
  # MySQL数据库服务
  mysql:
    image: mysql:5.7
    platform: linux/amd64
    container_name: audit-mysql
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: 123456
      MYSQL_DATABASE: auditTrain
      MYSQL_CHARACTER_SET_SERVER: utf8mb4
      MYSQL_COLLATION_SERVER: utf8mb4_unicode_ci
      TZ: Asia/Shanghai
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./sql:/docker-entrypoint-initdb.d
      - ./mysql/conf:/etc/mysql/conf.d
    command: --default-authentication-plugin=mysql_native_password
    networks:
      - audit-network

  # Redis缓存服务
  redis:
    image: redis:6.0-alpine
    platform: linux/amd64
    container_name: audit-redis
    restart: always
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    networks:
      - audit-network

volumes:
  mysql_data:
  redis_data:

networks:
  audit-network:
    driver: bridge