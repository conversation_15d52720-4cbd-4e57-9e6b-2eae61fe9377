package com.ruoyi.web.controller.system;

import javax.websocket.*;
import javax.websocket.server.ServerEndpoint;
import java.io.IOException;
import java.util.logging.Logger;

@ServerEndpoint("/openai")
public class OpenAIWebSocket {

    private static final Logger logger = Logger.getLogger(OpenAIWebSocket.class.getName());
    private static final String OPENAI_API_KEY = "***********************************************************************************************************************************************************************************************************************";
    private static final String OPENAI_API_URL = "https://api.openai.com/v1/chat/completions";
    //private static final String OPENAI_API_KEY = "sk-a3184118210148019384a4af70334799";
    //private static final String OPENAI_API_URL = "https://api.aihao123.cn/luomacode-api/open-api/v1/chat/completions";
    @OnOpen
    public void onOpen(Session session) {
        logger.info("Connected: " + session.getId());
    }

    @OnMessage
    public void onMessage(String message, Session session) throws IOException {
        logger.info("Received: " + message);
        System.out.print("Received: " + message);
        // Call OpenAI API
        String openAIResponse = callOpenAI(message);

        // Send the response back to the client
        session.getBasicRemote().sendText(openAIResponse);
    }

    @OnClose
    public void onClose(Session session) {
        logger.info("Disconnected: " + session.getId());
    }

    @OnError
    public void onError(Session session, Throwable throwable) {
        logger.severe("Error: " + throwable.getMessage());
    }

    private String callOpenAI(String userMessage) {
        try {
            // Prepare the request payload
            String jsonPayload = String.format(
                    "{\"model\": \"gpt-3.5-turbo\", \"messages\": [{\"role\": \"user\", \"content\": \"%s\"}]}",
                    userMessage
            );

            // Create the HTTP request
            okhttp3.RequestBody body = okhttp3.RequestBody.create(
                    okhttp3.MediaType.parse("application/json"),
                    jsonPayload
                        );

            okhttp3.Request request = new okhttp3.Request.Builder()
                    .url(OPENAI_API_URL)
                    .post(body)
                    .addHeader("Authorization", "Bearer " + OPENAI_API_KEY)
                    .addHeader("Content-Type", "application/json")
                    .build();

            // Execute the request
            okhttp3.OkHttpClient client = new okhttp3.OkHttpClient();
            okhttp3.Response response = client.newCall(request).execute();

            if (response.isSuccessful()) {
                // Parse the response
                String responseBody = response.body().string();
                return new com.google.gson.JsonParser()
                        .parse(responseBody)
                        .getAsJsonObject()
                        .getAsJsonArray("choices")
                        .get(0)
                        .getAsJsonObject()
                        .getAsJsonObject("message")
                        .get("content")
                        .getAsString();
            } else {
                return "Error: " + response.message();
            }
        } catch (Exception e) {
            e.printStackTrace();
            return "Error calling OpenAI API: " + e.getMessage();
        }
    }
}