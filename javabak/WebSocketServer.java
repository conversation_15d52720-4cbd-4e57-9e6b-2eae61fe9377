package com.ruoyi.framework.webSocket;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.tencentcloudapi.hunyuan.v20230901.models.Choice;
import com.tencentcloudapi.hunyuan.v20230901.models.Message;
import com.tencentcloudapi.tione.v20211111.models.ChatCompletionResponse;
import com.theokanning.openai.completion.chat.ChatMessage;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.TextMessage;
import org.springframework.web.socket.WebSocketSession;

import javax.websocket.*;
import javax.websocket.server.PathParam;
import javax.websocket.server.ServerEndpoint;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.atomic.AtomicInteger;

import com.tencentcloudapi.common.Credential;
import com.tencentcloudapi.common.exception.TencentCloudSDKException;
import com.tencentcloudapi.common.profile.ClientProfile;
import com.tencentcloudapi.common.profile.HttpProfile;
import com.tencentcloudapi.nlp.v20190408.NlpClient;
import com.tencentcloudapi.hunyuan.v20230901.HunyuanClient;
import com.tencentcloudapi.hunyuan.v20230901.models.ChatCompletionsRequest;
import com.tencentcloudapi.hunyuan.v20230901.models.ChatCompletionsResponse;

@Component
@ServerEndpoint("/websocket/{userId}")
public class WebSocketServer {
    private static final Logger log = LoggerFactory.getLogger(WebSocketServer.class);
    //private static final String API_KEY = "sk-48gq6t21d38utb7dgao6aj1j2vo1tpt6b1c32s9hv362fuuq";
    private static final String API_KEY = "sk-6bfa6406b29c4928ba4af783e318d9df";
    //private static final String API_URL = "https://api.aihao123.cn/luomacode-api/open-api/v1/chat/data";
    private static final String API_URL = "https://api.deepseek.com/chat/completions";

    //混元secretId
    private static  final String secretId="AKIDscBcrWDrLchZDesumSvAE7mDQRI1WN7j";

    //混元secretKey
    private static  final String secretKey="Vs792lGahdP2TggT1LURBgx33xjKisru";


    //静态变量，用来记录当前在线连接数。应该把它设计成线程安全的。
    private static AtomicInteger onlineNum = new AtomicInteger();

    //concurrent包的线程安全Set，用来存放每个客户端对应的WebSocketServer对象。
    private static ConcurrentHashMap<String, Session> sessionPools = new ConcurrentHashMap<>();

    /**
     * 线程安全list，用来存放 在线客户端账号
     */
    public static List<String> userList = new CopyOnWriteArrayList<>();


    /**
     * 连接成功
     * @param session
     * @param userId
     */
    @OnOpen
    public void onOpen(Session session, @PathParam(value = "userId") String userId) {
        log.debug("我有session吗：" + session.getClass());
        sessionPools.put(userId, session);
        if (!userList.contains(userId)) {
            addOnlineCount();
            userList.add(userId);
        }
        log.debug("ID为【" + userId + "】的用户加入websocket！当前在线人数为：" + onlineNum);
        log.debug("当前在线：" + userList);
    }

    /**
     * 关闭连接
     * @param userId
     */
    @OnClose
    public void onClose(@PathParam(value = "userId") String userId) {
        sessionPools.remove(userId);
        if (userList.contains(userId)) {
            userList.remove(userId);
            subOnlineCount();
        }
        log.debug(userId + "断开webSocket连接！当前人数为" + onlineNum);

    }

    /**
     * 消息监听
     * @param message
     * @throws IOException
     */
    @OnMessage
    public void onMessage(String message) throws IOException {
        try {
            JSONObject jsonObject = JSONObject.parseObject(message);
            String userId = jsonObject.getString("userId");
            String type = jsonObject.getString("type");
            if (type.equals(MessageType.CHAT.getType())) {
                log.debug("聊天消息推送");
                log.debug(jsonObject.toString());
                sendToUser(userId, JSONObject.toJSONString(jsonObject));
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private String callHunyuan(String userMessage)
    {
        String strReturn="";
        try {
            // 1. 初始化认证信息
            Credential cred = new Credential(secretId, secretKey);

            // 2. 配置 HTTP 和客户端
            HttpProfile httpProfile = new HttpProfile();
            httpProfile.setEndpoint("hunyuan.tencentcloudapi.com"); // 混元大模型的 API 地址
            ClientProfile clientProfile = new ClientProfile();
            clientProfile.setHttpProfile(httpProfile);

            // 3. 初始化客户端
            HunyuanClient client = new HunyuanClient(cred, "ap-guangzhou", clientProfile);

            // 4. 构建请求
            ChatCompletionsRequest req = new ChatCompletionsRequest();
            req.setModel("hunyuan-turbo");
            Message[] messages = new Message[1]; // 设置输入文本
            Message message = new Message();
            message.setRole("user"); // 设置消息发送者的角色，例如"user"或"assistant"
            message.setContent(userMessage); // 设置消息内容

            req.setMessages(new Message[]{message}); // 设置输入文本
            req.setTemperature(0.7f); // 设置生成温度

            // 5. 发送请求并获取响应
            ChatCompletionsResponse resp = client.ChatCompletions(req);
            Choice[] choiceList=resp.getChoices();
            /*for (Choice choice : choiceList)  {
                System.out.println("choice="+choice.getMessage().getContent());
                strReturn+=choice.getMessage().getContent();
            }*/
            strReturn+=choiceList[0].getMessage().getContent();
            System.out.println("Response: " + resp.toString());
            return strReturn;
        } catch (TencentCloudSDKException e) {
            System.err.println("Error: " + e.getMessage());
            return "Error: " + e.getMessage();
        }
    }


    private String callOpenAI(String userMessage) throws IOException {
        JsonObject requestBody = new JsonObject();
        JsonArray messages = new JsonArray();

        JsonObject message = new JsonObject();
        message.addProperty("role", "user");
        message.addProperty("content", userMessage);
        messages.add(message);

        //requestBody.add("model", JsonParser.parseString("gpt-3.5-turbo"));
        requestBody.add("model", JsonParser.parseString("deepseek-chat"));
        requestBody.add("messages", messages);

        // 创建 HTTP 连接
        URL url = new URL(API_URL);
        System.out.println("API_URL2345="+API_URL);
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();
        connection.setRequestMethod("POST");
        connection.setRequestProperty("Authorization", "Bearer " + API_KEY);
        connection.setRequestProperty("Content-Type", "application/json");
        connection.setDoOutput(true);

        System.out.println(connection.getOutputStream());
        // 发送请求体
        try (OutputStream os = connection.getOutputStream()) {
            byte[] input = requestBody.toString().getBytes("utf-8");
            os.write(input, 0, input.length);
        }

        // 读取响应
        try (BufferedReader in = new BufferedReader(new InputStreamReader(connection.getInputStream(), "utf-8"))) {
            StringBuilder response = new StringBuilder();
            String inputLine;
            while ((inputLine = in.readLine()) != null) {
                response.append(inputLine);
            }
            /*ObjectMapper mapper = new ObjectMapper();
            DeepSeekResponse deepSeekResponse = mapper.readValue(response, DeepSeekResponse.class);
            System.out.println("Field1: " + deepSeekResponse.getField1());
            System.out.println("Field2: " + deepSeekResponse.getField2());

            System.out.println("Response: " + responseBody);
            strReturn+=deepSeekResponse.getField1();
            strReturn+=deepSeekResponse.getField2();*/
            return response.toString();  // 返回 OpenAI 的响应
        }

    }



    /**
     * 连接错误
     * @param session
     * @param throwable
     * @throws IOException
     */
    @OnError
    public void onError(Session session, Throwable throwable) throws IOException {
        log.error("websocket连接错误！");
        throwable.printStackTrace();
    }

    /**
     * 发送消息
     */
    public void sendMessage(Session session, String message) throws IOException, EncodeException {
        if (session != null) {
            // 快速返回 true.。这个很重要若不快速返回，客户端就回不再监听onmessage事件，
            // 这样就无法获得返回给客户端的信息
            session.getAsyncRemote().sendText("true");
            synchronized (session) {
                // 调用 OpenAI API 获取响应
                String openAIResponse = callOpenAI(message);

                ObjectMapper mapper = new ObjectMapper();
                try {
                    // 解析 JSON
                    DeepSeekResponse response = mapper.readValue(openAIResponse, DeepSeekResponse.class);

                    // 提取数据
                    System.out.println("ID: " + response.getId());
                    System.out.println("Model: " + response.getModel());
                    System.out.println("Assistant Message: " + response.getChoices().get(0).getMessage().getContent());
                    System.out.println("Total Tokens: " + response.getUsage().getTotalTokens());
                    // 将 OpenAI 的响应发送回客户端
                    session.getBasicRemote().sendText(response.getChoices().get(0).getMessage().getContent());
                } catch (Exception e) {
                    e.printStackTrace();
                }
                System.out.println("openAIResponse: " + openAIResponse);
                // 将 OpenAI 的响应发送回客户端
                //session.sendMessage(new TextMessage(openAIResponse));
            }

        }
    }

    /**
     * 给指定用户发送信息
     */
    public String sendToUser(String userId, String message) {

        String strReturn="";
        Session session = sessionPools.get(userId);
        try {
            if (session != null) {
                sendMessage(session, message);
            }else {
                log.debug("推送用户不在线");
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return strReturn;
    }

    public static void addOnlineCount() {
        onlineNum.incrementAndGet();
    }

    public static void subOnlineCount() {
        onlineNum.decrementAndGet();

    }
}