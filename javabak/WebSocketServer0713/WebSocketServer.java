package com.ruoyi.framework.webSocket;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.websocket.*;
import javax.websocket.server.PathParam;
import javax.websocket.server.ServerEndpoint;
import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.atomic.AtomicInteger;

@Component
@ServerEndpoint("/websocket/{userId}")
public class WebSocketServer {
    private static final Logger log = LoggerFactory.getLogger(WebSocketServer.class);

    private static final String API_KEY = "sk-6bfa6406b29c4928ba4af783e318d9df";
    //private static final String API_URL = "https://api.aihao123.cn/luomacode-api/open-api/v1/chat/data";
    private static final String API_URL = "https://api.deepseek.com/chat/completions";

    private static final String SYSTEM_PROMPT =
            "你是一名ai问答员，如果客户问你以下（或类似）内容的问题，你需要严格按照以下指示回答：\n" +
                    "问：王老师您好，我们项目上监盘现金的时候发现了2000元短款。是管理层12月借走了，我们已经核实了业务的真实性。但是这家公司打算整笔业务1月一并做账，我们不确定这个会计处理对不对呀，所以咨询下您！\n" +
                    "答：这个问题在审计中非常典型，根据您描述的情况——这是一起“白条抵库”的情形，也就是说，在资产负债表日，公司用一张还未正式入账的借条代替了实际现金的存在。从审计准则和实务要求来看，这种处理方式是不符合规范的，因此我们应将其视为错报，并建议做出相应的审计调整。\n" +
                    "问：哦哦，明白了，那对于这种错报，我们应当怎么样进行审计应对呢？\n" +
                    "答：第一，在审计底稿中明确记录该事项属于错报，列明差异金额、产生原因、影响的财务报表项目及审计认定类别（如涉及存在性、合法性等）；第二，建议公司调整会计处理：在12月31日资产负债表日，应将该借条计入“其他应收款”，并减少“库存现金”，实现资产的真实反映；第三，对内部控制的有效性做进一步评估：建议将该事项作为“内部控制执行不到位”的典型案例，写入管理建议函，并在与治理层沟通时提出；第四，如果管理层拒绝调整，应将该事项写入管理层函证中，并评估其是否对审计意见产生影响，必要时在审计报告中提请使用者关注。\n" +
                    "问：周老师您好！我们在监盘现金的时候发现存在2000元的短缺，这个是什么原因呢？\n" +
                    "答：哎呀，是这样，应该是韩总在12月10号晚上接待客户时急用钱，临时借走的。当时事情比较急，我们就没来得及办理正式借款手续。他写了一张借条，盖了章，后来在1月份已经补办了正式报销手续了。你们去查1月3号的报销单就可以看到了。\n" +
                    "问：哦哦，我了解了，所以在借款当月，也就是12月，你们财务这边没有进行账务处理是吗？\n" +
                    "答：对的，是1月才做的账，但其实业务是真实发生的，也有借条、发票、审批表，手续都是齐的，只是入账时间晚了几天。\n" +
                    "问：周老师您好，12月2000元的借款是存在错报的哦，因为你们公司用一张还未正式入账的借条代替了实际现金的存在，这个是白条抵库是违规的\n" +
                    "答：好的好的，明白了。那么这个应该如何进行账项调整呢\n" +
                    "问：你们需要在12月31日资产负债表日，应将该借条计入“其他应收款”，并减少“库存现金”，实现资产的真实反映\n" +
                    "答：明白了，您说的非常正确，我今天就去把这个账调整过来，谢谢审计老师专业的解答！";

    private static AtomicInteger onlineNum = new AtomicInteger();
    private static ConcurrentHashMap<String, Session> sessionPools = new ConcurrentHashMap<>();
    public static List<String> userList = new CopyOnWriteArrayList<>();

    @OnOpen
    public void onOpen(Session session, @PathParam(value = "userId") String userId) {
        log.debug("我有session吗：" + session.getClass());
        sessionPools.put(userId, session);
        if (!userList.contains(userId)) {
            addOnlineCount();
            userList.add(userId);
        }
        log.debug("ID为【" + userId + "】的用户加入websocket！当前在线人数为：" + onlineNum);
        log.debug("当前在线：" + userList);
    }

    @OnClose
    public void onClose(@PathParam(value = "userId") String userId) {
        sessionPools.remove(userId);
        if (userList.contains(userId)) {
            userList.remove(userId);
            subOnlineCount();
        }
        log.info("用户【{}】离开，当前在线人数：{}", userId, onlineNum.get());
    }

    @OnMessage
    public void onMessage(String message) throws IOException {
        try {
            JSONObject jsonObject = JSONObject.parseObject(message);
            String userId = jsonObject.getString("userId");
            String type = jsonObject.getString("type");
            if (type.equals(MessageType.CHAT.getType())) {
                log.debug("聊天消息推送");
                log.debug(jsonObject.toString());
                sendToUser(userId, JSONObject.toJSONString(jsonObject));
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @OnError
    public void onError(Session session, Throwable throwable) {
        log.error("websocket连接错误", throwable);
    }

    public void sendMessage(Session session, String message) throws IOException, EncodeException {
        if (session == null) {
            return;
        }

        session.getAsyncRemote().sendText("true");

        synchronized (session) {
            String openAIResponse = callOpenAI(message);
            ObjectMapper mapper = new ObjectMapper();

            try {
                String trimmed = openAIResponse.trim();

                if (!trimmed.startsWith("{")) {
                    // 非 JSON，直接发原文
                    System.out.println("非 JSON 响应，直接返回原文");
                    session.getBasicRemote().sendText(openAIResponse);
                    return;
                }

                // 检查并修复 JSON 中的 detail 字段
                String safeJson = fixJsonDetailField(trimmed);

                if (safeJson.contains("\"id\"") && safeJson.contains("\"choices\"")) {
                    // 🌟 DeepSeek 返回
                    DeepSeekResponse response = mapper.readValue(safeJson, DeepSeekResponse.class);
                    String content = response.getChoices().get(0).getMessage().getContent();
                    System.out.println("DeepSeek Assistant Message: " + content);
                    session.getBasicRemote().sendText(content);

                } else {
                    // 🌟 业务封装消息
                    JsonNode jsonNode = mapper.readTree(safeJson);
                    if (jsonNode.has("detail")) {
                        String detail = jsonNode.get("detail").asText();
                        System.out.println("业务 detail: " + detail);
                        session.getBasicRemote().sendText(detail);
                    } else {
                        System.out.println("JSON 响应里没有 detail 字段");
                        session.getBasicRemote().sendText(openAIResponse);
                    }
                }

            } catch (Exception e) {
                e.printStackTrace();
                session.getBasicRemote().sendText("解析响应出错，原始内容如下：\n" + openAIResponse);
            }

            System.out.println("openAIResponse: " + openAIResponse);
        }
    }

    /**
     * 修复 JSON 中 detail 字段里未转义的引号
     */
    private String fixJsonDetailField(String json) {
        try {
            // 正常解析
            ObjectMapper mapper = new ObjectMapper();
            JsonNode root = mapper.readTree(json);

            if (root.has("detail")) {
                String detail = root.get("detail").asText();
                // 如果值里有未转义的 "
                String fixedDetail = detail.replace("\"", "\\\"");
                ((com.fasterxml.jackson.databind.node.ObjectNode) root).put("detail", fixedDetail);
                return mapper.writeValueAsString(root);
            } else {
                return json;
            }
        } catch (Exception e) {
            System.out.println("无法修复 detail 字段，返回原始 JSON");
            return json;
        }
    }



    /*public void sendMessage(Session session, String message) throws IOException, EncodeException {
        if (session != null) {
            // 快速返回 true
            session.getAsyncRemote().sendText("true");
            synchronized (session) {
                String openAIResponse = callOpenAI(message);

                ObjectMapper mapper = new ObjectMapper();
                try {
                    if (openAIResponse.contains("\"id\"") && openAIResponse.contains("\"choices\"")) {
                        // 🌟 DeepSeek 返回
                        DeepSeekResponse response = mapper.readValue(openAIResponse, DeepSeekResponse.class);
                        String content = response.getChoices().get(0).getMessage().getContent();
                        System.out.println("DeepSeek Assistant Message: " + content);
                        session.getBasicRemote().sendText(content);

                    } else {
                        // 🌟 业务封装消息
                        JsonNode jsonNode = mapper.readTree(openAIResponse);
                        if (jsonNode.has("detail")) {
                            String detail = jsonNode.get("detail").asText();
                            System.out.println("业务 detail: " + detail);
                            session.getBasicRemote().sendText(detail);
                        } else {
                            System.out.println("响应里没有 detail 字段");
                            session.getBasicRemote().sendText(openAIResponse);
                        }
                    }

                } catch (Exception e) {
                    e.printStackTrace();
                    session.getBasicRemote().sendText("解析响应出错: " + e.getMessage());
                }

                System.out.println("openAIResponse: " + openAIResponse);
            }
        }
    }*/


    /*public void sendMessage(Session session, String message) throws IOException, EncodeException {
        if (session != null) {
            // 快速返回 true.。这个很重要若不快速返回，客户端就回不再监听onmessage事件，
            // 这样就无法获得返回给客户端的信息
            session.getAsyncRemote().sendText("true");
            synchronized (session) {
                // 调用 OpenAI API 获取响应
                String openAIResponse = callOpenAI(message);

                ObjectMapper mapper = new ObjectMapper();
                try {
                    // 解析 JSON
                    if (openAIResponse.contains("\"id\"") && openAIResponse.contains("\"choices\"")) {
                        // 是 DeepSeek 返回
                        DeepSeekResponse response = mapper.readValue(openAIResponse, DeepSeekResponse.class);
                        String content = response.getChoices().get(0).getMessage().getContent();
                        session.getBasicRemote().sendText(content);
                        // 提取数据
                        System.out.println("ID: " + response.getId());
                        System.out.println("Model: " + response.getModel());
                        System.out.println("Assistant Message: " + response.getChoices().get(0).getMessage().getContent());
                        System.out.println("Total Tokens: " + response.getUsage().getTotalTokens());
                        session.getBasicRemote().sendText(response.getChoices().get(0).getMessage().getContent());
                    } else {
                        // 是自己封装的业务消息
                        session.getBasicRemote().sendText(openAIResponse);
                    }


                    // 将 OpenAI 的响应发送回客户端

                } catch (Exception e) {
                    e.printStackTrace();
                }
                System.out.println("openAIResponse: " + openAIResponse);
                // 将 OpenAI 的响应发送回客户端
                //session.sendMessage(new TextMessage(openAIResponse));
            }

        }
    }*/

    /**
     * 给指定用户发送信息
     */
    public String sendToUser(String userId, String message) {

        String strReturn="";
        Session session = sessionPools.get(userId);
        try {
            if (session != null) {
                sendMessage(session, message);
            }else {
                log.debug("推送用户不在线");
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return strReturn;
    }

    /**
     * 核心：带上 SYSTEM_PROMPT 的 OpenAI 请求
     */
    private String callOpenAI(String userMessage) throws IOException {
        JsonObject requestBody = new JsonObject();
        JsonArray messages = new JsonArray();

        // system prompt
        JsonObject systemMessage = new JsonObject();
        systemMessage.addProperty("role", "system");
        systemMessage.addProperty("content", SYSTEM_PROMPT);
        messages.add(systemMessage);

        // user message
        JsonObject userMsg = new JsonObject();
        userMsg.addProperty("role", "user");
        userMsg.addProperty("content", userMessage);
        messages.add(userMsg);

        requestBody.add("model", JsonParser.parseString("\"deepseek-chat\""));
        requestBody.add("messages", messages);

        URL url = new URL(API_URL);
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();
        connection.setRequestMethod("POST");
        connection.setRequestProperty("Authorization", "Bearer " + API_KEY);
        connection.setRequestProperty("Content-Type", "application/json");
        connection.setDoOutput(true);

        try (OutputStream os = connection.getOutputStream()) {
            byte[] input = requestBody.toString().getBytes("utf-8");
            os.write(input, 0, input.length);
        }

        try (BufferedReader in = new BufferedReader(new InputStreamReader(connection.getInputStream(), "utf-8"))) {
            StringBuilder response = new StringBuilder();
            String inputLine;
            while ((inputLine = in.readLine()) != null) {
                response.append(inputLine);
            }

            JsonObject json = JsonParser.parseString(response.toString()).getAsJsonObject();
            return json.getAsJsonArray("choices")
                    .get(0).getAsJsonObject()
                    .getAsJsonObject("message")
                    .get("content").getAsString();
        }
    }

    private static void addOnlineCount() {
        onlineNum.incrementAndGet();
    }

    private static void subOnlineCount() {
        onlineNum.decrementAndGet();
    }
}
