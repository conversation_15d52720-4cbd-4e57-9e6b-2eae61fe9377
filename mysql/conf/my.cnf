[mysqld]
default-storage-engine=INNODB
character-set-server=utf8mb4
collation-server=utf8mb4_unicode_ci
init_connect='SET NAMES utf8mb4'

# 性能优化
max_connections=1000
innodb_buffer_pool_size=256M
innodb_log_file_size=128M
innodb_log_buffer_size=16M
key_buffer_size=64M

# 时区设置
default-time-zone='+8:00'

# 兼容性设置
sql_mode=STRICT_TRANS_TABLES,NO_ZERO_DATE,NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_AUTO_CREATE_USER,NO_ENGINE_SUBSTITUTION

[mysql]
default-character-set=utf8mb4

[client]
default-character-set=utf8mb4