package com.ruoyi.web.controller.system;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.Bill;
import com.ruoyi.system.service.IBillService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 账单Controller
 *
 * <AUTHOR>
 * @date 2025-03-05
 */
@RestController
@RequestMapping("/system/bill")
public class BillController extends BaseController
{
    @Autowired
    private IBillService billService;

    /**
     * 查询账单列表
     */
    //@PreAuthorize("@ss.hasPermi('system:bill:list')")
    @GetMapping("/list")
    public TableDataInfo list(Bill bill)
    {
        startPage();
        List<Bill> list = billService.selectBillList(bill);
        return getDataTable(list);
    }

    /**
     * 导出账单列表
     */
    //@PreAuthorize("@ss.hasPermi('system:bill:export')")
    @Log(title = "账单", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, Bill bill)
    {
        List<Bill> list = billService.selectBillList(bill);
        ExcelUtil<Bill> util = new ExcelUtil<Bill>(Bill.class);
        util.exportExcel(response, list, "账单数据");
    }

    /**
     * 获取账单详细信息
     */
    //@PreAuthorize("@ss.hasPermi('system:bill:query')")
    @GetMapping(value = "/{billId}")
    public AjaxResult getInfo(@PathVariable("billId") Long billId)
    {
        return success(billService.selectBillByBillId(billId));
    }

    /**
     * 新增账单
     */
    //@PreAuthorize("@ss.hasPermi('system:bill:add')")
    @Log(title = "账单", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody Bill bill)
    {
        return toAjax(billService.insertBill(bill));
    }

    /**
     * 修改账单
     */
    //@PreAuthorize("@ss.hasPermi('system:bill:edit')")
    @Log(title = "账单", businessType = BusinessType.UPDATE)
    @PutMapping("/update")
    public AjaxResult edit(@RequestBody Bill bill)
    {
        return toAjax(billService.updateBill(bill));
    }

    @Log(title = "账单图片", businessType = BusinessType.UPDATE)
    @PutMapping("/update-img")
    public AjaxResult editImg(@RequestBody Bill bill)
    {
        System.out.println(bill);
        return toAjax(billService.updateBillImg(bill));
    }

    /**
     * 删除账单
     */
    //@PreAuthorize("@ss.hasPermi('system:bill:remove')")
    @Log(title = "账单", businessType = BusinessType.DELETE)
    @DeleteMapping("/{billIds}")
    public AjaxResult remove(@PathVariable Long[] billIds)
    {
        return toAjax(billService.deleteBillByBillIds(billIds));
    }
}
