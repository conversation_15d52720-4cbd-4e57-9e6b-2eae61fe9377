package com.ruoyi.web.controller.system;

import java.io.Console;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.Chathistory;
import com.ruoyi.system.service.IChathistoryService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.system.domain.TreeNode;
import com.ruoyi.common.utils.SecurityUtils;

/**
 * 聊天记录Controller
 *
 * <AUTHOR>
 * @date 2025-02-28
 */
@RestController
@RequestMapping("/system/chathistory")
public class ChathistoryController extends BaseController
{
    @Autowired
    private IChathistoryService chathistoryService;

    /**
     * 查询聊天记录列表
     */
    @GetMapping("/list")
    public TableDataInfo list(Chathistory chathistory)
    {
        startPage();
        List<Chathistory> list = chathistoryService.selectChathistoryList(chathistory);
        return getDataTable(list);
    }

    @GetMapping("/tree/{chatId}")
    public List<TreeNode> selectTreedayList(@PathVariable("chatId") Long chatId)
    {
        System.out.println("chatId="+chatId);
        List<TreeNode> treeData = new ArrayList<>();

        Long userId = chatId;

        List<Chathistory> list=chathistoryService.selectTreedayList(userId);

        // 直接返回扁平化的聊天记录列表，不按日期分组
        for (Chathistory chathistory : list) {
            // 使用chatContent作为label，这样前端可以直接解析消息
            TreeNode node = new TreeNode(
                chathistory.getChatId().toString(), 
                chathistory.getChatContent(), 
                "0"
            );
            treeData.add(node);
        }

        return treeData;
    }

    /**
     * 导出聊天记录列表
     */
    @Log(title = "聊天记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, Chathistory chathistory)
    {
        List<Chathistory> list = chathistoryService.selectChathistoryList(chathistory);
        ExcelUtil<Chathistory> util = new ExcelUtil<Chathistory>(Chathistory.class);
        util.exportExcel(response, list, "聊天记录数据");
    }

    /**
     * 获取聊天记录详细信息
     */
    @GetMapping(value = "/{chatId}")
    public AjaxResult getInfo(@PathVariable("chatId") Long chatId)
    {
        System.out.println("chatId="+chatId);
        return success(chathistoryService.selectChathistoryByChatId(chatId));
    }

    /**
     * 新增聊天记录
     */
    @Log(title = "聊天记录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody Chathistory chathistory)
    {
        return toAjax(chathistoryService.insertChathistory(chathistory));
    }

    /**
     * 修改聊天记录
     */
    @Log(title = "聊天记录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody Chathistory chathistory)
    {
        return toAjax(chathistoryService.updateChathistory(chathistory));
    }

    /**
     * 删除聊天记录
     */
    @Log(title = "聊天记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{chatIds}")
    public AjaxResult remove(@PathVariable Long[] chatIds)
    {
        return toAjax(chathistoryService.deleteChathistoryByChatIds(chatIds));
    }
}
