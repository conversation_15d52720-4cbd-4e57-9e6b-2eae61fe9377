package com.ruoyi.web.controller.system;

import com.ruoyi.common.config.RuoYiConfig;
import com.ruoyi.system.domain.ExcelInfo;
import com.ruoyi.system.service.IExcelInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.file.FileUploadUtils;
import org.apache.poi.openxml4j.exceptions.InvalidFormatException;
import org.apache.poi.openxml4j.opc.OPCPackage;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;

@RestController
@RequestMapping("/system/excel")
public class ExcelController {

    @Value("${ruoyi.profile}")
    private String uploadDir;

    @Autowired
    private IExcelInfoService excelInfoService;

    @PostMapping("/upload")
    public AjaxResult uploadFile(@RequestParam("file") MultipartFile file) {
        if (file.isEmpty()) {
            return AjaxResult.error("请选择一个文件上传");
        }

        try {
            // 调用工具类上传文件
            String filePath = FileUploadUtils.upload(file);
            return AjaxResult.success("文件上传成功", filePath);
        } catch (IOException e) {
            e.printStackTrace();
            return AjaxResult.error("文件上传失败: " + e.getMessage());
        }
    }

    @GetMapping("/download")
    public void downloadFile(@RequestParam String id, HttpServletResponse response) throws UnsupportedEncodingException {

        String encodedFileName = null;
        response.setHeader("Content-Disposition", "attachment; filename=\"" + encodedFileName + "\"");

        ExcelInfo exceInfo = excelInfoService.selectExcelInfoByInfoId(Long.decode(id));
        // 文件路径
        String filePath = exceInfo.getExcelPath();
        filePath = filePath.replaceAll("/profile/", RuoYiConfig.getProfile());

        File file = new File(filePath);

        if (file.exists()) {
            response.setCharacterEncoding(StandardCharsets.UTF_8.name());
            encodedFileName = URLEncoder.encode(exceInfo.getExcelName(), "UTF-8").replaceAll("\\+", "%20");
            response.setContentType("application/octet-stream");
            response.setHeader("Content-Disposition", "attachment; filename=" + encodedFileName);
            response.setContentLengthLong(file.length());

            try (FileInputStream fis = new FileInputStream(file);
                 OutputStream os = response.getOutputStream()) {

                byte[] buffer = new byte[10240];
                int bytesRead;
                while ((bytesRead = fis.read(buffer)) != -1) {
                    os.write(buffer, 0, bytesRead);
                }
                os.flush();
            } catch (IOException e) {
                // 处理异常
                e.printStackTrace();
            }
        } else {
            // 处理文件不存在的情况
            response.setStatus(HttpServletResponse.SC_NOT_FOUND);
        }
    }

    /**
     * 读取 Excel 文件并解析数据，支持公式计算
     */
    /*@PostMapping("/upload")
    public AjaxResult uploadExcel(@RequestParam("file") MultipartFile file) {
        List<List<String>> data = new ArrayList<>();
        try {
            OPCPackage opcPackage = OPCPackage.open(file.getInputStream());
            File file1 = new File("output.xlsx");
            opcPackage.save(file1);
            Workbook workbook = WorkbookFactory.create(file.getInputStream());
            Sheet sheet = workbook.getSheetAt(0);
            FormulaEvaluator evaluator = workbook.getCreationHelper().createFormulaEvaluator();
            for (Row row : sheet) {
                List<String> rowData = new ArrayList<>();
                for (Cell cell : row) {
                    switch (cell.getCellType()) {
                        case STRING:
                            rowData.add(cell.getStringCellValue());
                            break;
                        case NUMERIC:
                            rowData.add(String.valueOf(cell.getNumericCellValue()));
                            break;
                        case FORMULA:
                            try {
                                rowData.add(evaluator.evaluate(cell).formatAsString());

                            }
                            catch (Exception e){
                                System.out.println("e.getMessage()="+e.getMessage());
                            }
                            break;
                        default:
                            rowData.add("");
                    }
                }

                data.add(rowData);

            }

            workbook.close();
        } catch (IOException e) {
            return AjaxResult.error("文件读取失败");
        } catch (InvalidFormatException e) {
            throw new RuntimeException(e);
        }

        return AjaxResult.success(data);
    }*/

    /**
     * 保存 Excel 数据
     */
    @PostMapping("/save")
    public AjaxResult saveExcel(@RequestBody List<List<String>> data) {
        try {
            Workbook workbook = new XSSFWorkbook();
            Sheet sheet = workbook.createSheet("Sheet1");

            for (int i = 0; i < data.size(); i++) {
                Row row = sheet.createRow(i);
                for (int j = 0; j < data.get(i).size(); j++) {
                    row.createCell(j).setCellValue(data.get(i).get(j));
                }
            }

            FileOutputStream fileOut = new FileOutputStream("output.xlsx");
            workbook.write(fileOut);
            fileOut.close();
            workbook.close();
        } catch (IOException e) {
            return AjaxResult.error("文件保存失败");
        }
        return AjaxResult.success("文件保存成功");
    }
}
