package com.ruoyi.web.controller.system;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.config.RuoYiConfig;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.file.FileUploadUtils;
import com.ruoyi.system.domain.Chathistory;
import com.ruoyi.system.domain.TreeNode;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.ExcelInfo;
import com.ruoyi.system.service.IExcelInfoService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import org.springframework.web.multipart.MultipartFile;

/**
 * 【请填写功能名称】Controller
 *
 * <AUTHOR>
 * @date 2025-03-19
 */
@RestController
@RequestMapping("/system/info")
public class ExcelInfoController extends BaseController
{
    @Autowired
    private IExcelInfoService excelInfoService;

    /**
     * 查询【请填写功能名称】列表
     */
    //@PreAuthorize("@ss.hasPermi('system:info:list')")
    @GetMapping("/list")
    public TableDataInfo list(ExcelInfo excelInfo)
    {
        System.out.println("list实验");
        startPage();
        List<ExcelInfo> list = excelInfoService.selectExcelInfoList(excelInfo);
        return getDataTable(list);
    }

    @GetMapping("/exceltree")
    public List<TreeNode> selectTreedayList()
    {
        ExcelInfo excelInfo=new ExcelInfo();
        List<TreeNode> treeData = new ArrayList<>();

        Long userId = SecurityUtils.getUserId();
        List<ExcelInfo> list=excelInfoService.selectExcelInfoList(excelInfo);
        //System.out.println("list.size()="+String.valueOf(list.size()));
        for (ExcelInfo exc: list) {
            TreeNode node=new TreeNode(String.valueOf(exc.getInfoId()), exc.getExcelName(), "0");
            treeData.add(node);
        }
        return treeData;
    }

    /**
     * 导出【请填写功能名称】列表
     */
    //@PreAuthorize("@ss.hasPermi('system:info:export')")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ExcelInfo excelInfo)
    {
        List<ExcelInfo> list = excelInfoService.selectExcelInfoList(excelInfo);
        ExcelUtil<ExcelInfo> util = new ExcelUtil<ExcelInfo>(ExcelInfo.class);
        util.exportExcel(response, list, "【请填写功能名称】数据");
    }

    /**
     * 获取【请填写功能名称】详细信息
     */
    //@PreAuthorize("@ss.hasPermi('system:info:query')")
    @GetMapping(value = "/{infoId}")
    public AjaxResult getInfo(@PathVariable("infoId") Long infoId)
    {
        return success(excelInfoService.selectExcelInfoByInfoId(infoId));
    }

    /**
     * 新增【请填写功能名称】
     */
    //@PreAuthorize("@ss.hasPermi('system:info:add')")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ExcelInfo excelInfo)
    {
        return toAjax(excelInfoService.insertExcelInfo(excelInfo));
    }

    /**
     * 修改【请填写功能名称】
     */
    //@PreAuthorize("@ss.hasPermi('system:info:edit')")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ExcelInfo excelInfo)
    {
        return toAjax(excelInfoService.updateExcelInfo(excelInfo));
    }

    /**
     * 删除【请填写功能名称】
     */
    //@PreAuthorize("@ss.hasPermi('system:info:remove')")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.DELETE)
    @DeleteMapping("/{infoIds}")
    public AjaxResult remove(@PathVariable Long[] infoIds)
    {
        for(Long infoId:infoIds){
            ExcelInfo excelInfo=excelInfoService.selectExcelInfoByInfoId(infoId);
            String path=excelInfo.getExcelPath();
            path=path.replaceAll("/profile/",RuoYiConfig.getProfile());
            if(path!=null){
                deleteFile(path);
            }
        }
        return toAjax(excelInfoService.deleteExcelInfoByInfoIds(infoIds));
    }

    public boolean deleteFile(String filePath) {
        try {
            // 调用工具类上传文件
            File file = new File(filePath);
            return file.delete();
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }
}
