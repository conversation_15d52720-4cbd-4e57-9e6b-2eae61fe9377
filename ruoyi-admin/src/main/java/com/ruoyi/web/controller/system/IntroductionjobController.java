package com.ruoyi.web.controller.system;

import java.io.IOException;
import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.config.RuoYiConfig;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.file.FileUploadUtils;
import com.ruoyi.system.service.impl.OpenAIService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.Introductionjob;
import com.ruoyi.system.service.IIntroductionjobService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import org.springframework.web.multipart.MultipartFile;

/**
 * 项目介绍信息Controller
 *
 * <AUTHOR>
 * @date 2025-02-13
 */
@RestController
@RequestMapping("/system/introductionjob")
public class IntroductionjobController extends BaseController
{
    @Autowired
    private IIntroductionjobService introductionjobService;

    @Autowired
    private OpenAIService openAIService;

    @GetMapping("/completion")
    public String getCompletion(@RequestParam String prompt) {
        return openAIService.getCompletion(prompt);
    }

    /**
     * 查询项目介绍信息列表
     */
    //@PreAuthorize("@ss.hasPermi('system:introductionjob:list')")
    @GetMapping("/list")
    public TableDataInfo list(Introductionjob introductionjob)
    {
        startPage();
        List<Introductionjob> list = introductionjobService.selectIntroductionjobList(introductionjob);
        return getDataTable(list);
    }

    /**
     * 导出项目介绍信息列表
     */
    //@PreAuthorize("@ss.hasPermi('system:introductionjob:export')")
    @Log(title = "项目介绍信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, Introductionjob introductionjob)
    {
        List<Introductionjob> list = introductionjobService.selectIntroductionjobList(introductionjob);
        ExcelUtil<Introductionjob> util = new ExcelUtil<Introductionjob>(Introductionjob.class);
        util.exportExcel(response, list, "项目介绍信息数据");
    }

    /**
     * 获取项目介绍信息详细信息
     */
    //@PreAuthorize("@ss.hasPermi('system:introductionjob:query')")
    @GetMapping(value = "/{intrId}")
    public AjaxResult getInfo(@PathVariable("intrId") Long intrId)
    {
        return success(introductionjobService.selectIntroductionjobByIntrId(intrId));
    }

    /**
     * 新增项目介绍信息
     */
    //@PreAuthorize("@ss.hasPermi('system:introductionjob:add')")
    @Log(title = "项目介绍信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody Introductionjob introductionjob)
    {
        System.out.println("introductionjob.getImageData()="+introductionjob.getImageData());
        return toAjax(introductionjobService.insertIntroductionjob(introductionjob));
    }

    @Log(title = "预览缩略图", businessType = BusinessType.UPDATE)
    @PostMapping("/articleImg")
    public AjaxResult uploadFile(MultipartFile file) throws IOException
    {
        if (!file.isEmpty())
        {
            String articleImg = FileUploadUtils.upload(RuoYiConfig.getArticleImgPath(), file);
            if (!StringUtils.isEmpty(articleImg))
            {
                AjaxResult ajax = AjaxResult.success();
                ajax.put("articleImg", articleImg);
                return ajax;
            }
        }
        return AjaxResult.error("上传图片异常，请联系管理员");
    }

    @Log(title = "预览缩略图", businessType = BusinessType.UPDATE)
    @PostMapping("/billImg")
    public AjaxResult billImg(MultipartFile file) throws IOException
    {
        if (!file.isEmpty())
        {
            String articleImg = FileUploadUtils.upload(RuoYiConfig.getBillimg(), file);
            if (!StringUtils.isEmpty(articleImg))
            {
                AjaxResult ajax = AjaxResult.success();
                ajax.put("articleImg", articleImg);
                return ajax;
            }
        }
        return AjaxResult.error("上传图片异常，请联系管理员");
    }


    /**
     * 修改项目介绍信息
     */
    //@PreAuthorize("@ss.hasPermi('system:introductionjob:edit')")
    @Log(title = "项目介绍信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody Introductionjob introductionjob)
    {
        return toAjax(introductionjobService.updateIntroductionjob(introductionjob));
    }

    /**
     * 删除项目介绍信息
     */
    //@PreAuthorize("@ss.hasPermi('system:introductionjob:remove')")
    @Log(title = "项目介绍信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{intrIds}")
    public AjaxResult remove(@PathVariable Long[] intrIds)
    {
        return toAjax(introductionjobService.deleteIntroductionjobByIntrIds(intrIds));
    }
}
