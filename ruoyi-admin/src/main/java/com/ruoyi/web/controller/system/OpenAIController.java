package com.ruoyi.web.controller.system;

import com.ruoyi.common.core.controller.BaseController;

import com.ruoyi.system.service.impl.OpenAIService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/system/openai")
public class OpenAIController extends BaseController {

    @Autowired
    private OpenAIService openAIService;

    @GetMapping("/completion")
    public String ai的对话功能(@RequestParam String prompt) {
        System.out.print("调用到我了");
        return openAIService.getCompletion(prompt);
    }
}
