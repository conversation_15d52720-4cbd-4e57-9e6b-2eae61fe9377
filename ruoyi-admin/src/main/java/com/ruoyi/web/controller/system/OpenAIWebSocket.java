package com.ruoyi.web.controller.system;

import javax.websocket.*;
import javax.websocket.server.ServerEndpoint;
import java.io.IOException;
import java.util.logging.Logger;

@ServerEndpoint("/openai")
public class OpenAIWebSocket {

    private static final Logger logger = Logger.getLogger(OpenAIWebSocket.class.getName());

    private static final String OPENAI_API_KEY = "sk-3cdf9adfc5bb48708a1a3f527472e2f8";
    private static final String OPENAI_API_URL = "https://api.deepseek.com/v1";

    // ✅ 修改点：新增完整 system prompt
    private static final String SYSTEM_PROMPT =
            "你是一名ai问答员，如果客户问你以下（或类似）内容的问题，你需要严格按照以下指示回答：\n" +
                    "问：王老师您好，我们项目上监盘现金的时候发现了2000元短款。是管理层12月借走了，我们已经核实了业务的真实性。但是这家公司打算整笔业务1月一并做账，我们不确定这个会计处理对不对呀，所以咨询下您！\n" +
                    "答：这个问题在审计中非常典型，根据您描述的情况——这是一起“白条抵库”的情形，也就是说，在资产负债表日，公司用一张还未正式入账的借条代替了实际现金的存在。从审计准则和实务要求来看，这种处理方式是不符合规范的，因此我们应将其视为错报，并建议做出相应的审计调整。\n" +
                    "问：哦哦，明白了，那对于这种错报，我们应当怎么样进行审计应对呢？\n" +
                    "答：第一，在审计底稿中明确记录该事项属于错报，列明差异金额、产生原因、影响的财务报表项目及审计认定类别（如涉及存在性、合法性等）；第二，建议公司调整会计处理：在12月31日资产负债表日，应将该借条计入“其他应收款”，并减少“库存现金”，实现资产的真实反映；第三，对内部控制的有效性做进一步评估：建议将该事项作为“内部控制执行不到位”的典型案例，写入管理建议函，并在与治理层沟通时提出；第四，如果管理层拒绝调整，应将该事项写入管理层函证中，并评估其是否对审计意见产生影响，必要时在审计报告中提请使用者关注。\n" +
                    "问：周老师您好！我们在监盘现金的时候发现存在2000元的短缺，这个是什么原因呢？\n" +
                    "答：哎呀，是这样，应该是韩总在12月10号晚上接待客户时急用钱，临时借走的。当时事情比较急，我们就没来得及办理正式借款手续。他写了一张借条，盖了章，后来在1月份已经补办了正式报销手续了。你们去查1月3号的报销单就可以看到了。\n" +
                    "问：哦哦，我了解了，所以在借款当月，也就是12月，你们财务这边没有进行账务处理是吗？\n" +
                    "答：对的，是1月才做的账，但其实业务是真实发生的，也有借条、发票、审批表，手续都是齐的，只是入账时间晚了几天。\n" +
                    "问：周老师您好，12月2000元的借款是存在错报的哦，因为你们公司用一张还未正式入账的借条代替了实际现金的存在，这个是白条抵库是违规的\n" +
                    "答：好的好的，明白了。那么这个应该如何进行账项调整呢\n" +
                    "问：你们需要在12月31日资产负债表日，应将该借条计入“其他应收款”，并减少“库存现金”，实现资产的真实反映\n" +
                    "答：明白了，您说的非常正确，我今天就去把这个账调整过来，谢谢审计老师专业的解答！";

    @OnOpen
    public void onOpen(Session session) {
        logger.info("Connected: " + session.getId());
    }

    @OnMessage
    public void onMessage(String message, Session session) throws IOException {
        logger.info("Received: " + message);
        System.out.println("Received: " + message);
        String openAIResponse = callOpenAI(message);
        session.getBasicRemote().sendText(openAIResponse);
    }

    @OnClose
    public void onClose(Session session) {
        logger.info("Disconnected: " + session.getId());
    }

    @OnError
    public void onError(Session session, Throwable throwable) {
        logger.severe("Error: " + throwable.getMessage());
    }

    // ✅ 修改点：在 messages 中加入完整 system prompt（无换行替换，直接原样使用）
    private String callOpenAI(String userMessage) {
        try {
            String safeSystemPrompt = SYSTEM_PROMPT.replace("\"", "\\\"");  // 防止引号导致 JSON 格式错误
            String safeUserMessage = userMessage.replace("\"", "\\\"");

            String jsonPayload = String.format(
                    "{\n" +
                            "  \"model\": \"deepseek-chat\",\n" +
                            "  \"messages\": [\n" +
                            "    {\"role\": \"system\", \"content\": \"%s\"},\n" +
                            "    {\"role\": \"user\", \"content\": \"%s\"}\n" +
                            "  ]\n" +
                            "}", safeSystemPrompt, safeUserMessage
            );

            okhttp3.RequestBody body = okhttp3.RequestBody.create(
                    okhttp3.MediaType.parse("application/json"),
                    jsonPayload
            );

            okhttp3.Request request = new okhttp3.Request.Builder()
                    .url(OPENAI_API_URL)
                    .post(body)
                    .addHeader("Authorization", "Bearer " + OPENAI_API_KEY)
                    .addHeader("Content-Type", "application/json")
                    .build();

            okhttp3.OkHttpClient client = new okhttp3.OkHttpClient();
            okhttp3.Response response = client.newCall(request).execute();

            if (response.isSuccessful()) {
                String responseBody = response.body().string();
                return new com.google.gson.JsonParser()
                        .parse(responseBody)
                        .getAsJsonObject()
                        .getAsJsonArray("choices")
                        .get(0)
                        .getAsJsonObject()
                        .getAsJsonObject("message")
                        .get("content")
                        .getAsString();
            } else {
                return "Error: " + response.message();
            }
        } catch (Exception e) {
            e.printStackTrace();
            return "Error calling OpenAI API: " + e.getMessage();
        }
    }
}