package com.ruoyi.web.controller.system;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.SysFileCategory;
import com.ruoyi.system.service.ISysFileCategoryService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

@RestController
@RequestMapping("/system/category")
public class SysFileCategoryController extends BaseController {
    @Autowired
    private ISysFileCategoryService sysFileCategoryService;

    @GetMapping("/list")
    public TableDataInfo list(SysFileCategory sysFileCategory) {
        startPage();
        List<SysFileCategory> list = sysFileCategoryService.selectSysFileCategoryList(sysFileCategory);
        return getDataTable(list);
    }

    @GetMapping(value = "/{categoryId}")
    public AjaxResult getInfo(@PathVariable("categoryId") Long categoryId) {
        return AjaxResult.success(sysFileCategoryService.selectSysFileCategoryById(categoryId));
    }

    @PostMapping
    public AjaxResult add(@RequestBody SysFileCategory sysFileCategory) {
        return toAjax(sysFileCategoryService.insertSysFileCategory(sysFileCategory));
    }

    @PutMapping
    public AjaxResult edit(@RequestBody SysFileCategory sysFileCategory) {
        return toAjax(sysFileCategoryService.updateSysFileCategory(sysFileCategory));
    }

    @DeleteMapping("/{categoryIds}")
    public AjaxResult remove(@PathVariable Long[] categoryIds) {
        return toAjax(sysFileCategoryService.deleteSysFileCategoryByIds(categoryIds));
    }
}
