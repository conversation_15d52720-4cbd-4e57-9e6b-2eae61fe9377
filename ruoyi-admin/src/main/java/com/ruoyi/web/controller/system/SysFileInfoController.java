package com.ruoyi.web.controller.system;

import java.io.File;
import java.io.FileInputStream;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.util.Date;
import java.util.List;

import com.ruoyi.common.utils.file.FileUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.SysFileInfo;
import com.ruoyi.system.service.ISysFileInfoService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.utils.file.FileUploadUtils;
import com.ruoyi.common.config.RuoYiConfig;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;

@RestController
@RequestMapping("/system/fileInfo")
public class SysFileInfoController extends BaseController {
    @Autowired
    private ISysFileInfoService sysFileInfoService;

    @GetMapping("/list")
    public TableDataInfo list(SysFileInfo sysFileInfo) {

        startPage();
        List<SysFileInfo> list = sysFileInfoService.selectSysFileInfoList(sysFileInfo);
        return getDataTable(list);
    }

    @GetMapping(value = "/{fileId}")
    public AjaxResult getInfo(@PathVariable("fileId") Long fileId) {
        return AjaxResult.success(sysFileInfoService.selectSysFileInfoById(fileId));
    }

    @PostMapping
    public AjaxResult add(@RequestBody SysFileInfo sysFileInfo) {
        return toAjax(sysFileInfoService.insertSysFileInfo(sysFileInfo));
    }

    @PutMapping
    public AjaxResult edit(@RequestBody SysFileInfo sysFileInfo) {
        return toAjax(sysFileInfoService.updateSysFileInfo(sysFileInfo));
    }

    @DeleteMapping("/{fileIds}")
    public AjaxResult remove(@PathVariable Long[] fileIds) {
        return toAjax(sysFileInfoService.deleteSysFileInfoByIds(fileIds));
    }

    @PostMapping("/upload")
    public AjaxResult upload(@RequestParam("file") MultipartFile file) {
        if (file.isEmpty()) {
            return AjaxResult.error("上传文件不能为空");
        }
        try {
            // 1️⃣ 保存文件
            String uploadDir = RuoYiConfig.getUploadPath(); // 若依默认上传路径
            String fileName = FileUploadUtils.upload(uploadDir, file);
            String fileUrl = "/upload/" + fileName;

            // 2️⃣ 保存记录（可选，如果前台再调 /system/file 保存，这里可以不插库）
            SysFileInfo fileInfo = new SysFileInfo();
            fileInfo.setFileName(file.getOriginalFilename());
            fileInfo.setFilePath(fileName);
            fileInfo.setFileUrl(fileUrl);
            fileInfo.setFileSize(file.getSize());
            fileInfo.setCreateTime(new Date());
            // 不在这里插库，而是返回前台后再调 /system/file

            return AjaxResult.success(fileInfo);
        } catch (Exception e) {
            logger.error("上传文件失败", e);
            return AjaxResult.error("上传文件异常: " + e.getMessage());
        }
    }

    /*@GetMapping("/download")
    public void download(@RequestParam("fileName") String fileName, HttpServletResponse response) {
        try {
            SysFileInfo sysFileInfo= sysFileInfoService.selectSysFileInfoByName(fileName);
            String realPath = sysFileInfo.getFilePath();
            String profile = RuoYiConfig.getProfile();
            realPath = realPath.replaceFirst("/profile", profile);
            System.out.println(realPath);
            // 设置响应头
            response.reset();
            response.setHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode(fileName, "UTF-8"));
            response.setContentType("application/octet-stream");
            System.out.println("ertyuio");
            // 读文件写到输出流
            FileUtils.writeBytes(realPath, response.getOutputStream());
        } catch (Exception e) {
            logger.error("下载文件失败", e);
        }
    }*/

    @GetMapping("/download")
    public void download(@RequestParam("fileName") String fileName, HttpServletResponse response) {
        try {
            SysFileInfo sysFileInfo= sysFileInfoService.selectSysFileInfoByName(fileName);
            String realPath = sysFileInfo.getFilePath();
            String profile = RuoYiConfig.getProfile();
            realPath = realPath.replaceFirst("/profile", profile);
            System.out.println(realPath);
            // 设置响应头
            response.reset();
            response.setHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode(fileName, "UTF-8"));
            response.setContentType("application/octet-stream");
            File file = new File(realPath);

            if (!file.exists()) {
                response.sendError(404, "文件不存在");
                return;
            }

            response.reset();
            response.setContentType("application/force-download");

            String encodedFileName = URLEncoder.encode(fileName, "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-Disposition", "attachment; filename*=UTF-8''" + encodedFileName);

            FileInputStream fis = new FileInputStream(file);
            OutputStream os = response.getOutputStream();

            byte[] buffer = new byte[1024];
            int len;
            while ((len = fis.read(buffer)) != -1) {
                os.write(buffer, 0, len);
            }
            fis.close();
            os.flush();
        } catch (Exception e) {
            logger.error("下载文件失败", e);
        }
    }

}
