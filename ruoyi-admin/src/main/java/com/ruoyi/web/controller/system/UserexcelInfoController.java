package com.ruoyi.web.controller.system;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.utils.SecurityUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.UserexcelInfo;
import com.ruoyi.system.service.IUserexcelInfoService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 【请填写功能名称】Controller
 *
 * <AUTHOR>
 * @date 2025-03-24
 */
@RestController
@RequestMapping("/system/userexcel")
public class UserexcelInfoController extends BaseController
{
    @Autowired
    private IUserexcelInfoService userexcelInfoService;

    /**
     * 查询【请填写功能名称】列表
     */
    @GetMapping("/list")
    public TableDataInfo list(UserexcelInfo userexcelInfo)
    {
        startPage();
        Long userId = SecurityUtils.getUserId();
        userexcelInfo.setUserId(userId);
        List<UserexcelInfo> list = userexcelInfoService.selectUserexcelInfoList(userexcelInfo);
        return getDataTable(list);
    }

    /**
     * 导出【请填写功能名称】列表
     */
    @Log(title = "【请填写功能名称】", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, UserexcelInfo userexcelInfo)
    {
        List<UserexcelInfo> list = userexcelInfoService.selectUserexcelInfoList(userexcelInfo);
        ExcelUtil<UserexcelInfo> util = new ExcelUtil<UserexcelInfo>(UserexcelInfo.class);
        util.exportExcel(response, list, "【请填写功能名称】数据");
    }

    /**
     * 获取【请填写功能名称】详细信息
     */

    @GetMapping(value = "/{userexcelId}")
    public AjaxResult getInfo(@PathVariable("userexcelId") Long userexcelId)
    {
        UserexcelInfo userexcelInfo=userexcelInfoService.selectUserexcelInfoByUserexcelId(userexcelId);
        return success(userexcelInfo);
    }

    /**
     * 新增【请填写功能名称】
     */
    @Log(title = "【请填写功能名称】", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody UserexcelInfo userexcelInfo)
    {
        return toAjax(userexcelInfoService.insertUserexcelInfo(userexcelInfo));
    }

    /**
     * 修改【请填写功能名称】
     */
    @Log(title = "【请填写功能名称】", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody UserexcelInfo userexcelInfo)
    {

        return toAjax(userexcelInfoService.updateUserexcelInfo(userexcelInfo));
    }

    /**
     * 删除【请填写功能名称】
     */

    @Log(title = "【请填写功能名称】", businessType = BusinessType.DELETE)
    @DeleteMapping("/{userexcelIds}")
    public AjaxResult remove(@PathVariable Long[] userexcelIds)
    {
        return toAjax(userexcelInfoService.deleteUserexcelInfoByUserexcelIds(userexcelIds));
    }
}

