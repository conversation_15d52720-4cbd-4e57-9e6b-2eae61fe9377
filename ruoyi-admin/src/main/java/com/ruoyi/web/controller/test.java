package com.ruoyi.web.controller;


import okhttp3.*;

import java.io.IOException;

public class test {
        // 腾讯混元大模型的API地址
        private static final String API_URL = "https://api.hunyuan.cloud.tencent.com/v1/chat/completions";

        // 你的API密钥
        private static final String API_KEY = "sk-kKD3q6ss0WH1cz49P1WicUrprR5u6OqJwy7tt1PKf0nuG4ml";

        public static void main(String[] args) {
            // 创建OkHttp客户端
            OkHttpClient client = new OkHttpClient();

            // 构建请求体
            String json = "{\"prompt\": \"你好，混元大模型\", \"max_tokens\": 50}";
            RequestBody body = RequestBody.create(MediaType.parse("application/json"),json );

            // 构建请求
            Request request = new Request.Builder()
                    .url(API_URL)
                    .post(body)
                    .addHeader("Authorization", "Bearer " + API_KEY)
                    .addHeader("Content-Type", "application/json")
                    .build();

            // 发送请求并处理响应
            try (Response response = client.newCall(request).execute()) {
                if (response.isSuccessful() && response.body() != null) {
                    String responseBody = response.body().string();
                    System.out.println("Response: " + responseBody);
                } else {
                    System.out.println("Request failed with code: " + response.code());
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }

}
