package com.ruoyi.common.config;

import javax.annotation.PostConstruct;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 读取项目相关配置
 *
 */
@Component
@ConfigurationProperties(prefix = "ruoyi")
public class RuoYiConfig
{
    /** 项目名称 */
    private String name;

    /** 版本 */
    private String version;

    /** 版权年份 */
    private String copyrightYear;

    /** 上传路径 */
    private String profile;

    /** 账单图片路径 */
    private String billimg;

    /** 获取地址开关 */
    private boolean addressEnabled;

    /** 验证码类型 */
    private String captchaType;

    /** static 变量 */
    private static RuoYiConfig instance;

    // 让 static 方法也能访问属性
    @PostConstruct
    public void init() {
        instance = this;
    }

    // 以下是 static 调用方式
    public static String getProfile() {
        return instance.profile;
    }

    public static String getBillimg() {
        return instance.billimg;
    }

    public static boolean isAddressEnabled() {
        return instance.addressEnabled;
    }

    public static String getCaptchaType() {
        return instance.captchaType;
    }

    // Getter 和 Setter 需要保留
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getCopyrightYear() {
        return copyrightYear;
    }

    public void setCopyrightYear(String copyrightYear) {
        this.copyrightYear = copyrightYear;
    }

    public void setProfile(String profile) {
        this.profile = profile;
    }

    public void setBillimg(String billimg) {
        this.billimg = billimg;
    }

    public void setAddressEnabled(boolean addressEnabled) {
        this.addressEnabled = addressEnabled;
    }

    public void setCaptchaType(String captchaType) {
        this.captchaType = captchaType;
    }

    // 一些工具路径拼接
    public static String getImportPath() {
        return getProfile() + "/import";
    }

    public static String getAvatarPath() {
        return getProfile() + "/avatar";
    }

    public static String getDownloadPath() {
        return getProfile() + "/download/";
    }

    public static String getArticleImgPath() {
        return getProfile() + "/articleImg";
    }

    public static String getUploadPath() {
        return getProfile() + "/upload";
    }
}
