package com.ruoyi.framework.webSocket;

import com.tencentcloudapi.common.Credential;
import com.tencentcloudapi.common.profile.ClientProfile;
import com.tencentcloudapi.common.profile.HttpProfile;
import com.tencentcloudapi.common.exception.TencentCloudSDKException;
import com.tencentcloudapi.hunyuan.v20230901.HunyuanClient;
import com.tencentcloudapi.hunyuan.v20230901.models.ChatCompletionsRequest;
import com.tencentcloudapi.hunyuan.v20230901.models.ChatCompletionsResponse;
import com.tencentcloudapi.hunyuan.v20230901.models.Message;

public class HunyuanApiExample {
    private static final String API_URL = "https://api.hunyuan.com/v1/predict";
    private static final String API_KEY = "sk-kKD3q6ss0WH1cz49P1WicUrprR5u6OqJwy7tt1PKf0nuG4ml";

    public static void main(String[] args) {
        try {
            // 1. 初始化认证信息
            Credential cred = new Credential("AKIDscBcrWDrLchZDesumSvAE7mDQRI1WN7j"
                    , "Vs792lGahdP2TggT1LURBgx33xjKisru");

            // 2. 配置 HTTP 和客户端
            HttpProfile httpProfile = new HttpProfile();
            httpProfile.setEndpoint("hunyuan.tencentcloudapi.com"); // 混元大模型的 API 地址
            ClientProfile clientProfile = new ClientProfile();
            clientProfile.setHttpProfile(httpProfile);

            // 3. 初始化客户端
            HunyuanClient client = new HunyuanClient(cred, "ap-guangzhou", clientProfile);

            // 4. 构建请求
            ChatCompletionsRequest req = new ChatCompletionsRequest();
            req.setModel("hunyuan-turbo");
            Message[] messages = new Message[1]; // 设置输入文本
            Message message = new Message();
            message.setRole("user"); // 设置消息发送者的角色，例如"user"或"assistant"
            message.setContent("你好，腾讯混元大模型！"); // 设置消息内容

            req.setMessages(new Message[]{message}); // 设置输入文本
            req.setTemperature(0.7f); // 设置生成温度

            // 5. 发送请求并获取响应
            ChatCompletionsResponse resp = client.ChatCompletions(req);
            System.out.println("Response: " + ChatCompletionsResponse.toJsonString(resp));
        } catch (TencentCloudSDKException e) {
            System.err.println("Error: " + e.getMessage());
        }
    }
}
