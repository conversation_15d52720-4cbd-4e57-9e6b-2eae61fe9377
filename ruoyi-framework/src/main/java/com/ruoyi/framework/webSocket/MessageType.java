package com.ruoyi.framework.webSocket;

/**
 * 消息类型
 */
public enum MessageType {

    SYS("sys", "系统消息"), CHAT("chat", "聊天消息");

    private String type;
    private String value;

    private MessageType(String type, String value) {
        this.type = type;
        this.value = value;
    }

    public String getType() {
        return type;
    }

    public String getValue() {
        return value;
    }
}