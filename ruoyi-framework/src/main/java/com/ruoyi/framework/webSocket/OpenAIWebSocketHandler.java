package com.ruoyi.framework.webSocket;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.CloseStatus;
import org.springframework.web.socket.WebSocketHandler;
import org.springframework.web.socket.TextMessage;
import org.springframework.web.socket.WebSocketSession;
import org.springframework.web.socket.handler.TextWebSocketHandler;
import org.apache.http.HttpEntity;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.entity.StringEntity;
import org.apache.http.util.EntityUtils;

import javax.websocket.Session;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URI;
import java.net.URL;

@Component
public class OpenAIWebSocketHandler extends TextWebSocketHandler {

    //private static final String API_KEY = "********************************************************************************************************************************************************************";  // 替换为你的 OpenAI API 密钥
    //private static final String API_URL = "https://api.openai.com/v1/chat/completions";

    private static final String API_KEY = "sk-48gq6t21d38utb7dgao6aj1j2vo1tpt6b1c32s9hv362fuuq";
    private static final String API_URL = "https://api.aihao123.cn/luomacode-api/open-api/v1/chat/completions";

    @Override
    public void afterConnectionEstablished(WebSocketSession session) throws Exception {
        System.out.println("Client connected: " + session.getId());
        session.sendMessage(new TextMessage("Welcome to WebSocket!"));
    }

    @Override
    public void afterConnectionClosed(WebSocketSession session, CloseStatus status) throws Exception {
        System.out.println("Client disconnected: " + session.getId());
    }

    @Override
    public void handleTextMessage(WebSocketSession session, TextMessage message) throws InterruptedException, IOException {
        // 获取客户端发来的消息
        System.out.println("session="+session.getClass());
        String prompt = message.getPayload();
        System.out.println("Received message from client: " + prompt);

        // 调用 OpenAI API 获取响应
        String openAIResponse = callOpenAI(prompt);

        System.out.println("openAIResponse: " + openAIResponse);

        // 将 OpenAI 的响应发送回客户端
        session.sendMessage(new TextMessage(openAIResponse));
    }

    private String callOpenAI(String userMessage) throws IOException {
        JsonObject requestBody = new JsonObject();
        JsonArray messages = new JsonArray();

        JsonObject message = new JsonObject();
        message.addProperty("role", "user");
        message.addProperty("content", userMessage);
        messages.add(message);

        requestBody.add("model", JsonParser.parseString("gpt-3.5-turbo"));
        requestBody.add("messages", messages);

        // 创建 HTTP 连接
        URL url = new URL(API_URL);
        System.out.println("API_URL222="+API_URL);
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();
        connection.setRequestMethod("POST");
        connection.setRequestProperty("Authorization", "Bearer " + API_KEY);
        connection.setRequestProperty("Content-Type", "application/json");
        connection.setDoOutput(true);

        // 发送请求体
        try (OutputStream os = connection.getOutputStream()) {
            byte[] input = requestBody.toString().getBytes("utf-8");
            os.write(input, 0, input.length);
        }

        // 读取响应
        try (BufferedReader in = new BufferedReader(new InputStreamReader(connection.getInputStream(), "utf-8"))) {
            StringBuilder response = new StringBuilder();
            String inputLine;
            while ((inputLine = in.readLine()) != null) {
                response.append(inputLine);
            }
            return response.toString();  // 返回 OpenAI 的响应
        }
    }

    /*private String callOpenAI(String userMessage) {
        try {
            // Prepare the request payload
            String jsonPayload = String.format(
                    "{\"model\": \"gpt-3.5-turbo\", \"messages\": [{\"role\": \"user\", \"content\": \"%s\"}]}",
                    userMessage
            );

            // Create the HTTP request
            okhttp3.RequestBody body = okhttp3.RequestBody.create(
                    okhttp3.MediaType.parse("application/json"),
                    jsonPayload
            );
            System.out.println(API_URL);
            System.out.println(API_KEY);
            okhttp3.Request request = new okhttp3.Request.Builder()
                    .url(API_URL)
                    .post(body)
                    .addHeader("Authorization", "Bearer " + API_KEY)
                    .addHeader("Content-Type", "application/json")
                    .build();

            // Execute the request
            okhttp3.OkHttpClient client = new okhttp3.OkHttpClient();
            okhttp3.Response response = client.newCall(request).execute();

            if (response.isSuccessful()) {
                // Parse the response
                String responseBody = response.body().string();
                return new com.google.gson.JsonParser()
                        .parse(responseBody)
                        .getAsJsonObject()
                        .getAsJsonArray("choices")
                        .get(0)
                        .getAsJsonObject()
                        .getAsJsonObject("message")
                        .get("content")
                        .getAsString();
            } else {
                return "Error: " + response.message();
            }
        } catch (Exception e) {
            e.printStackTrace();
            return "Error calling OpenAI API: " + e.getMessage();
        }
    }*/

    // 调用 OpenAI API 的方法
    /*private String callOpenAI(String prompt) {System.out.println(123);

        String reply="";
        // 创建一个 HttpClient
        HttpClient client = newHttpClient();

        // 设置请求体
        JSONObject requestBody = new JSONObject();
        requestBody.put("model", "gpt-3.5-turbo"); // 使用 GPT-3.5 模型
        requestBody.put("temperature", 0.7);

        // 消息内容
        JSONObject message1 = new JSONObject();
        message1.put("role", "system");
        message1.put("content", "You are a helpful assistant.");

        JSONObject message2 = new JSONObject();
        message2.put("role", "user");
        message2.put("content", prompt);

        // 将消息添加到请求体
        requestBody.put("messages", new JSONObject[] { message1, message2 });

        // 创建 HTTP 请求
        HttpRequest request = HttpRequest.newBuilder()
                .uri(URI.create(API_URL))
                .header("Content-Type", "application/json")
                .header("Authorization", "Bearer " + API_KEY)
                .POST(HttpRequest.BodyPublishers.ofString(requestBody.toString()))
                .build();

        try {
            // 发送请求并获取响应
            HttpResponse<String> response = client.send(request, HttpResponse.BodyHandlers.ofString());

            // 检查响应状态
            if (response.statusCode() == 200) {
                // 解析并打印响应内容
                JSONObject jsonResponse = new JSONObject(Boolean.parseBoolean(response.body()));
                 reply = jsonResponse.getJSONArray("choices")
                        .getJSONObject(0)
                        .getJSONObject("message")
                        .getString("content");
                System.out.println("Assistant Reply: " + reply);
            } else {
                System.out.println("Request failed with status code: " + response.statusCode());
                System.out.println("Response: " + response.body());
            }
        } catch (Exception e) {
            System.out.println("getMessage: "+e.getMessage());
            e.printStackTrace();
        }
        return reply;
    }*/

}
