package com.ruoyi.system.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 账单对象 bill
 *
 * <AUTHOR>
 * @date 2025-04-01
 */
public class Bill extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 账单ID */
    private Long billId;

    /** 账单日期 */
    @Excel(name = "账单日期")
    private String billDate;

    /** 凭证号 */
    @Excel(name = "凭证号")
    private String voucherNo;

    /** 账单方向 */
    @Excel(name = "账单方向")
    private String billDirect;

    /** 一级科目 */
    @Excel(name = "一级科目")
    private String firstlevel;

    /** 二级科目 */
    @Excel(name = "二级科目")
    private String secondlevel;

    /** 三级科目 */
    @Excel(name = "三级科目")
    private String thirdlevel;

    /** 核算项目 */
    @Excel(name = "核算项目")
    private String accountproject;

    /** 发生额 */
    @Excel(name = "发生额")
    private String occurretamount;

    /** 余额方向 */
    @Excel(name = "余额方向")
    private String balanceDirect;

    /** 余额 */
    @Excel(name = "余额")
    private String balance;

    /** 备注 */
    @Excel(name = "备注")
    private String notes;

    /** 备用1 */
    @Excel(name = "备用1")
    private String bak1;

    /** 备用2 */
    @Excel(name = "备用2")
    private String bak2;

    /** 备用3 */
    @Excel(name = "备用3")
    private String bak3;

    /** 备用4 */
    @Excel(name = "备用4")
    private String bak4;

    /** 备用5 */
    @Excel(name = "备用5")
    private String bak5;

    /** 单据1 */
    @Excel(name = "单据1")
    private String doc1;

    /** 单据2 */
    @Excel(name = "单据2")
    private String doc2;

    /** 单据3 */
    @Excel(name = "单据3")
    private String doc3;

    /** 单据4 */
    @Excel(name = "单据4")
    private String doc4;

    /** 单据5 */
    @Excel(name = "单据5")
    private String doc5;

    public void setBillId(Long billId)
    {
        this.billId = billId;
    }

    public Long getBillId()
    {
        return billId;
    }
    public void setBillDate(String billDate)
    {
        this.billDate = billDate.replaceAll("//", "-");
    }

    public String getBillDate()
    {
        return billDate;
    }
    public void setVoucherNo(String voucherNo)
    {
        this.voucherNo = voucherNo;
    }

    public String getVoucherNo()
    {
        return voucherNo;
    }
    public void setBillDirect(String billDirect)
    {
        this.billDirect = billDirect;
    }

    public String getBillDirect()
    {
        return billDirect;
    }
    public void setFirstlevel(String firstlevel)
    {
        this.firstlevel = firstlevel;
    }

    public String getFirstlevel()
    {
        return firstlevel;
    }
    public void setSecondlevel(String secondlevel)
    {
        this.secondlevel = secondlevel;
    }

    public String getSecondlevel()
    {
        return secondlevel;
    }
    public void setThirdlevel(String thirdlevel)
    {
        this.thirdlevel = thirdlevel;
    }

    public String getThirdlevel()
    {
        return thirdlevel;
    }
    public void setAccountproject(String accountproject)
    {
        this.accountproject = accountproject;
    }

    public String getAccountproject()
    {
        return accountproject;
    }
    public void setOccurretamount(String occurretamount)
    {
        this.occurretamount = occurretamount;
    }

    public String getOccurretamount()
    {
        return occurretamount;
    }
    public void setBalanceDirect(String balanceDirect)
    {
        this.balanceDirect = balanceDirect;
    }

    public String getBalanceDirect()
    {
        return balanceDirect;
    }
    public void setBalance(String balance)
    {
        this.balance = balance;
    }

    public String getBalance()
    {
        return balance;
    }
    public void setNotes(String notes)
    {
        this.notes = notes;
    }

    public String getNotes()
    {
        return notes;
    }
    public void setBak1(String bak1)
    {
        if(bak1!=null) {
            this.bak1 = bak1.replaceAll("//", "-");
        }
    }

    public String getBak1()
    {
        return bak1;
    }
    public void setBak2(String bak2)
    {
        if(bak2!=null) {
            this.bak2 = bak2.replaceAll("//", "-");
        }
    }

    public String getBak2()
    {
        return bak2;
    }
    public void setBak3(String bak3)
    {
        if(bak3!=null) {
            this.bak3 = bak3.replaceAll("//", "-");
        }
    }

    public String getBak3()
    {
        return bak3;
    }
    public void setBak4(String bak4)
    {
        if(bak4!=null) {
            this.bak4 = bak4.replaceAll("//", "-");
        }
    }

    public String getBak4()
    {
        return bak4;
    }
    public void setBak5(String bak5)
    {
        if(bak5!=null)
        this.bak5 = bak5.replaceAll("//", "-");;
    }

    public String getBak5()
    {
        return bak5;
    }
    public void setDoc1(String doc1)
    {
        this.doc1 = doc1;
    }

    public String getDoc1()
    {
        return doc1;
    }
    public void setDoc2(String doc2)
    {
        this.doc2 = doc2;
    }

    public String getDoc2()
    {
        return doc2;
    }
    public void setDoc3(String doc3)
    {
        this.doc3 = doc3;
    }

    public String getDoc3()
    {
        return doc3;
    }
    public void setDoc4(String doc4)
    {
        this.doc4 = doc4;
    }

    public String getDoc4()
    {
        return doc4;
    }
    public void setDoc5(String doc5)
    {
        this.doc5 = doc5;
    }

    public String getDoc5()
    {
        return doc5;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
                .append("billId", getBillId())
                .append("billDate", getBillDate())
                .append("voucherNo", getVoucherNo())
                .append("billDirect", getBillDirect())
                .append("firstlevel", getFirstlevel())
                .append("secondlevel", getSecondlevel())
                .append("thirdlevel", getThirdlevel())
                .append("accountproject", getAccountproject())
                .append("occurretamount", getOccurretamount())
                .append("balanceDirect", getBalanceDirect())
                .append("balance", getBalance())
                .append("notes", getNotes())
                .append("bak1", getBak1())
                .append("bak2", getBak2())
                .append("bak3", getBak3())
                .append("bak4", getBak4())
                .append("bak5", getBak5())
                .append("doc1", getDoc1())
                .append("doc2", getDoc2())
                .append("doc3", getDoc3())
                .append("doc4", getDoc4())
                .append("doc5", getDoc5())
                .toString();
    }
}
