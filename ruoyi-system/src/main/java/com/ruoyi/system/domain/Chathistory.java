package com.ruoyi.system.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 聊天记录对象 chathistory
 *
 * <AUTHOR>
 * @date 2025-02-28
 */
public class Chathistory extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 聊天ID */
    private Long chatId;

    /** 用户id */
    @Excel(name = "用户id")
    private Long userId;

    /** 聊天标题 */
    @Excel(name = "聊天标题")
    private String chatTile;

    /** 聊天内容 */
    @Excel(name = "聊天内容")
    private String chatContent;

    /** 记录时间 */
    @Excel(name = "记录时间")
    private String chatTime;

    /** 备注 */
    @Excel(name = "备注")
    private String chatRemark;

    public void setChatId(Long chatId)
    {
        this.chatId = chatId;
    }

    public Long getChatId()
    {
        return chatId;
    }
    public void setUserId(Long userId)
    {
        this.userId = userId;
    }

    public Long getUserId()
    {
        return userId;
    }
    public void setChatTile(String chatTile)
    {
        this.chatTile = chatTile;
    }

    public String getChatTile()
    {
        return chatTile;
    }
    public void setChatContent(String chatContent)
    {
        this.chatContent = chatContent;
    }

    public String getChatContent()
    {
        return chatContent;
    }
    public void setChatTime(String chatTime)
    {
        this.chatTime = chatTime;
    }

    public String getChatTime()
    {
        return chatTime;
    }
    public void setChatRemark(String chatRemark)
    {
        this.chatRemark = chatRemark;
    }

    public String getChatRemark()
    {
        return chatRemark;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
                .append("chatId", getChatId())
                .append("userId", getUserId())
                .append("chatTile", getChatTile())
                .append("chatContent", getChatContent())
                .append("chatTime", getChatTime())
                .append("chatRemark", getChatRemark())
                .toString();
    }
}
