package com.ruoyi.system.domain;

import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 课程实体类
 *
 * <AUTHOR>
 * @date 2025-07-04
 */
public class Course extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 课程ID */
    private Long courseId;

    /** 课程标题 */
    private String title;

    /** 课程副标题 */
    private String subtitle;

    /** 讲师 */
    private String teacher;

    /** 讲师头像 */
    private String teacherAvatar;

    /** 上课日期 */
    private String date;

    /** 学生人数 */
    private Integer students;

    /** 状态 */
    private String status;

    /** 所属分类ID */
    private Long categoryId;

    /** 课程底稿 */
    private String draft;

    public Long getCourseId() {
        return courseId;
    }

    public void setCourseId(Long courseId) {
        this.courseId = courseId;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getSubtitle() {
        return subtitle;
    }

    public void setSubtitle(String subtitle) {
        this.subtitle = subtitle;
    }

    public String getTeacher() {
        return teacher;
    }

    public void setTeacher(String teacher) {
        this.teacher = teacher;
    }

    public String getTeacherAvatar() {
        return teacherAvatar;
    }

    public void setTeacherAvatar(String teacherAvatar) {
        this.teacherAvatar = teacherAvatar;
    }

    public String getDate() {
        return date;
    }

    public void setDate(String date) {
        this.date = date;
    }

    public Integer getStudents() {
        return students;
    }

    public void setStudents(Integer students) {
        this.students = students;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Long getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(Long categoryId) {
        this.categoryId = categoryId;
    }

    public String getDraft() {
        return draft;
    }

    public void setDraft(String draft) {
        this.draft = draft;
    }

    @Override
    public String toString() {
        return "Course{" +
                "courseId=" + courseId +
                ", title='" + title + '\'' +
                ", subtitle='" + subtitle + '\'' +
                ", teacher='" + teacher + '\'' +
                ", teacherAvatar='" + teacherAvatar + '\'' +
                ", date='" + date + '\'' +
                ", students=" + students +
                ", status='" + status + '\'' +
                ", categoryId=" + categoryId +
                ", draft='" + draft + '\'' +
                "} " + super.toString();
    }
}
