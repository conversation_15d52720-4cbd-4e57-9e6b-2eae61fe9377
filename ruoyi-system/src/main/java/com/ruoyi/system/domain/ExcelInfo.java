package com.ruoyi.system.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 【请填写功能名称】对象 excel_info
 *
 * <AUTHOR>
 * @date 2025-03-19
 */
public class ExcelInfo extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private Long infoId;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String excelName;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String excelPath;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String excelRemark;

    public void setInfoId(Long infoId)
    {
        this.infoId = infoId;
    }

    public Long getInfoId()
    {
        return infoId;
    }
    public void setExcelName(String excelName)
    {
        this.excelName = excelName;
    }

    public String getExcelName()
    {
        return excelName;
    }
    public void setExcelPath(String excelPath)
    {
        this.excelPath = excelPath;
    }

    public String getExcelPath()
    {
        return excelPath;
    }
    public void setExcelRemark(String excelRemark)
    {
        this.excelRemark = excelRemark;
    }

    public String getExcelRemark()
    {
        return excelRemark;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
                .append("infoId", getInfoId())
                .append("excelName", getExcelName())
                .append("excelPath", getExcelPath())
                .append("excelRemark", getExcelRemark())
                .toString();
    }
}
