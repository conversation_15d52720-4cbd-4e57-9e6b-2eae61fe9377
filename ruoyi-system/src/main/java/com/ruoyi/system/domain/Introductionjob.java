package com.ruoyi.system.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 项目介绍信息对象 introductionjob
 *
 * <AUTHOR>
 * @date 2025-02-13
 */
public class Introductionjob extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 项目介绍ID */
    private Long intrId;

    /** 项目介绍条目对应的菜单id，因为这个介绍要在菜单中显示 */
    @Excel(name = "项目介绍条目对应的菜单id，因为这个介绍要在菜单中显示")
    private Long menuId;

    /** 介绍内容 */
    @Excel(name = "介绍内容")
    private String intrContent;

    public String getImageData() {
        return imageData;
    }

    public void setImageData(String imageData) {
        this.imageData = imageData;
    }

    /** 存储图片数据 */
    @Excel(name = "存储图片数据")
    private String imageData;


    /** 备注 */
    @Excel(name = "标题")
    private String intrRemark;

    public void setIntrId(Long intrId)
    {
        this.intrId = intrId;
    }

    public Long getIntrId()
    {
        return intrId;
    }
    public void setMenuId(Long menuId)
    {
        this.menuId = menuId;
    }

    public Long getMenuId()
    {
        return menuId;
    }
    public void setIntrContent(String intrContent)
    {
        this.intrContent = intrContent;
    }

    public String getIntrContent()
    {
        return intrContent;
    }



    public void setIntrRemark(String intrRemark)
    {
        this.intrRemark = intrRemark;
    }

    public String getIntrRemark()
    {
        return intrRemark;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
                .append("intrId", getIntrId())
                .append("menuId", getMenuId())
                .append("intrContent", getIntrContent())
                .append("imageData", getImageData())
                .append("intrRemark", getIntrRemark())
                .toString();
    }
}
