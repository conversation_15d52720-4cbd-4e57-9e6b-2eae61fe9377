package com.ruoyi.system.domain;

import java.util.List;

public class TreeNode {
    private String id;          // 节点ID
    private String label;       // 节点名称
    private String parentId;    // 父节点ID
    private List<TreeNode> children; // 子节点列表

    // 构造函数
    public TreeNode(String id, String label, String parentId) {
        this.id = id;
        this.label = label;
        this.parentId = parentId;
    }

    // Getters and Setters
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public String getParentId() {
        return parentId;
    }

    public void setParentId(String parentId) {
        this.parentId = parentId;
    }

    public List<TreeNode> getChildren() {
        return children;
    }

    public void setChildren(List<TreeNode> children) {
        this.children = children;
    }
}
