package com.ruoyi.system.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 【请填写功能名称】对象 userexcel_info
 *
 * <AUTHOR>
 * @date 2025-03-24
 */
public class UserexcelInfo extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private Long userexcelId;

    /** 用户ID */
    @Excel(name = "用户ID")
    private Long userId;

    /** ExcelID */
    @Excel(name = "ExcelID")
    private Long excelId;

    private String userName;

    private  String excelName;

    public String getExcelName() {
        return excelName;
    }

    public void setExcelName(String excelName) {
        this.excelName = excelName;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    /** 文档日期 */
    @Excel(name = "文档日期")
    private String exceldocDate;

    /** 数据内容 */
    @Excel(name = "数据内容")
    private String exceldocContent;

    /** 备注 */
    @Excel(name = "备注")
    private String exceldocRemark;

    public void setUserexcelId(Long userexcelId)
    {
        this.userexcelId = userexcelId;
    }

    public Long getUserexcelId()
    {
        return userexcelId;
    }
    public void setUserId(Long userId)
    {
        this.userId = userId;
    }

    public Long getUserId()
    {
        return userId;
    }
    public void setExcelId(Long excelId)
    {
        this.excelId = excelId;
    }

    public Long getExcelId()
    {
        return excelId;
    }
    public void setExceldocDate(String exceldocDate)
    {
        this.exceldocDate = exceldocDate;
    }

    public String getExceldocDate()
    {
        return exceldocDate;
    }
    public void setExceldocContent(String exceldocContent)
    {
        this.exceldocContent = exceldocContent;
    }

    public String getExceldocContent()
    {
        return exceldocContent;
    }
    public void setExceldocRemark(String exceldocRemark)
    {
        this.exceldocRemark = exceldocRemark;
    }

    public String getExceldocRemark()
    {
        return exceldocRemark;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
                .append("userexcelId", getUserexcelId())
                .append("userId", getUserId())
                .append("excelId", getExcelId())
                .append("exceldocDate", getExceldocDate())
                .append("exceldocContent", getExceldocContent())
                .append("exceldocRemark", getExceldocRemark())
                .toString();
    }
}
