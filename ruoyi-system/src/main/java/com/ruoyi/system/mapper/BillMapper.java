package com.ruoyi.system.mapper;

import java.util.List;
import com.ruoyi.system.domain.Bill;

/**
 * 账单Mapper接口
 *
 * <AUTHOR>
 * @date 2025-03-31
 */
public interface BillMapper
{
    /**
     * 查询账单
     *
     * @param billId 账单主键
     * @return 账单
     */
    public Bill selectBillByBillId(Long billId);

    /**
     * 查询账单列表
     *
     * @param bill 账单
     * @return 账单集合
     */
    public List<Bill> selectBillList(Bill bill);

    /**
     * 新增账单
     *
     * @param bill 账单
     * @return 结果
     */
    public int insertBill(Bill bill);

    /**
     * 修改账单
     *
     * @param bill 账单
     * @return 结果
     */
    public int updateBill(Bill bill);

    public int updateBillImg(Bill bill);

    /**
     * 删除账单
     *
     * @param billId 账单主键
     * @return 结果
     */
    public int deleteBillByBillId(Long billId);

    /**
     * 批量删除账单
     *
     * @param billIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteBillByBillIds(Long[] billIds);
}
