package com.ruoyi.system.mapper;

import com.ruoyi.system.domain.Course;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 课程Mapper接口
 *
 * <AUTHOR> @date 2025-07-04
 */
public interface CourseMapper
{
    /**
     * 查询课程
     *
     * @param courseId 课程ID
     * @return 课程
     */
    Course selectCourseById(Long courseId);

    /**
     * 查询课程列表
     *
     * @param course 课程
     * @return 课程集合
     */
    List<Course> selectCourseList(Course course);

    /**
     * 新增课程
     *
     * @param course 课程
     * @return 结果
     */
    int insertCourse(Course course);

    /**
     * 修改课程
     *
     * @param course 课程
     * @return 结果
     */
    int updateCourse(Course course);

    /**
     * 删除课程
     *
     * @param courseId 课程ID
     * @return 结果
     */
    int deleteCourseById(Long courseId);

    /**
     * 批量删除课程
     *
     * @param courseIds 需要删除的数据ID
     * @return 结果
     */
    int deleteCourseByIds(Long[] courseIds);
}


