package com.ruoyi.system.mapper;

import java.util.List;
import com.ruoyi.system.domain.ExcelInfo;

/**
 * 【请填写功能名称】Mapper接口
 *
 * <AUTHOR>
 * @date 2025-03-19
 */
public interface ExcelInfoMapper
{
    /**
     * 查询【请填写功能名称】
     *
     * @param infoId 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    public ExcelInfo selectExcelInfoByInfoId(Long infoId);

    /**
     * 查询【请填写功能名称】列表
     *
     * @param excelInfo 【请填写功能名称】
     * @return 【请填写功能名称】集合
     */
    public List<ExcelInfo> selectExcelInfoList(ExcelInfo excelInfo);

    /**
     * 新增【请填写功能名称】
     *
     * @param excelInfo 【请填写功能名称】
     * @return 结果
     */
    public int insertExcelInfo(ExcelInfo excelInfo);

    /**
     * 修改【请填写功能名称】
     *
     * @param excelInfo 【请填写功能名称】
     * @return 结果
     */
    public int updateExcelInfo(ExcelInfo excelInfo);

    /**
     * 删除【请填写功能名称】
     *
     * @param infoId 【请填写功能名称】主键
     * @return 结果
     */
    public int deleteExcelInfoByInfoId(Long infoId);

    /**
     * 批量删除【请填写功能名称】
     *
     * @param infoIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteExcelInfoByInfoIds(Long[] infoIds);
}
