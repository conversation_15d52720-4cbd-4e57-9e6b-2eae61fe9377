package com.ruoyi.system.mapper;

import java.util.List;
import com.ruoyi.system.domain.Introductionjob;

/**
 * 项目介绍信息Mapper接口
 *
 * <AUTHOR>
 * @date 2025-02-13
 */
public interface IntroductionjobMapper
{
    /**
     * 查询项目介绍信息
     *
     * @param intrId 项目介绍信息主键
     * @return 项目介绍信息
     */
    public Introductionjob selectIntroductionjobByIntrId(Long intrId);

    /**
     * 查询项目介绍信息列表
     *
     * @param introductionjob 项目介绍信息
     * @return 项目介绍信息集合
     */
    public List<Introductionjob> selectIntroductionjobList(Introductionjob introductionjob);

    /**
     * 新增项目介绍信息
     *
     * @param introductionjob 项目介绍信息
     * @return 结果
     */
    public int insertIntroductionjob(Introductionjob introductionjob);

    /**
     * 修改项目介绍信息
     *
     * @param introductionjob 项目介绍信息
     * @return 结果
     */
    public int updateIntroductionjob(Introductionjob introductionjob);

    /**
     * 删除项目介绍信息
     *
     * @param intrId 项目介绍信息主键
     * @return 结果
     */
    public int deleteIntroductionjobByIntrId(Long intrId);

    /**
     * 批量删除项目介绍信息
     *
     * @param intrIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteIntroductionjobByIntrIds(Long[] intrIds);
}
