package com.ruoyi.system.mapper;

import java.util.List;
import com.ruoyi.system.domain.SysFileCategory;

public interface SysFileCategoryMapper {
    SysFileCategory selectSysFileCategoryById(Long categoryId);

    List<SysFileCategory> selectSysFileCategoryList(SysFileCategory sysFileCategory);

    int insertSysFileCategory(SysFileCategory sysFileCategory);

    int updateSysFileCategory(SysFileCategory sysFileCategory);

    int deleteSysFileCategoryById(Long categoryId);

    int deleteSysFileCategoryByIds(Long[] categoryIds);
}
