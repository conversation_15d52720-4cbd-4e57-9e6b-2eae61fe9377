package com.ruoyi.system.service;

import java.util.List;
import com.ruoyi.system.domain.Chathistory;

/**
 * 聊天记录Service接口
 *
 * <AUTHOR>
 * @date 2025-02-28
 */
public interface IChathistoryService
{
    /**
     * 查询聊天记录
     *
     * @param chatId 聊天记录主键
     * @return 聊天记录
     */
    public Chathistory selectChathistoryByChatId(Long chatId);

    /**
     * 查询聊天记录列表
     *
     * @param chathistory 聊天记录
     * @return 聊天记录集合
     */
    public List<Chathistory> selectChathistoryList(Chathistory chathistory);

    public List<Chathistory> selectTreedayList(Long userId);
    /**
     * 新增聊天记录
     *
     * @param chathistory 聊天记录
     * @return 结果
     */
    public int insertChathistory(Chathistory chathistory);

    /**
     * 修改聊天记录
     *
     * @param chathistory 聊天记录
     * @return 结果
     */
    public int updateChathistory(Chathistory chathistory);

    /**
     * 批量删除聊天记录
     *
     * @param chatIds 需要删除的聊天记录主键集合
     * @return 结果
     */
    public int deleteChathistoryByChatIds(Long[] chatIds);

    /**
     * 删除聊天记录信息
     *
     * @param chatId 聊天记录主键
     * @return 结果
     */
    public int deleteChathistoryByChatId(Long chatId);
}
