package com.ruoyi.system.service;

import java.util.List;
import com.ruoyi.system.domain.ExcelData;

/**
 * 【请填写功能名称】Service接口
 *
 * <AUTHOR>
 * @date 2025-03-12
 */
public interface IExcelDataService
{
    /**
     * 查询【请填写功能名称】
     *
     * @param id 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    public ExcelData selectExcelDataById(Long id);

    /**
     * 查询【请填写功能名称】列表
     *
     * @param excelData 【请填写功能名称】
     * @return 【请填写功能名称】集合
     */
    public List<ExcelData> selectExcelDataList(ExcelData excelData);

    /**
     * 新增【请填写功能名称】
     *
     * @param excelData 【请填写功能名称】
     * @return 结果
     */
    public int insertExcelData(ExcelData excelData);

    public void  saveData(List<ExcelData> excelData);

    /**
     * 修改【请填写功能名称】
     *
     * @param excelData 【请填写功能名称】
     * @return 结果
     */
    public int updateExcelData(ExcelData excelData);

    /**
     * 批量删除【请填写功能名称】
     *
     * @param ids 需要删除的【请填写功能名称】主键集合
     * @return 结果
     */
    public int deleteExcelDataByIds(Long[] ids);

    /**
     * 删除【请填写功能名称】信息
     *
     * @param id 【请填写功能名称】主键
     * @return 结果
     */
    public int deleteExcelDataById(Long id);
}
