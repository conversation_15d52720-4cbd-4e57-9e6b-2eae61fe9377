package com.ruoyi.system.service;

import java.util.List;
import com.ruoyi.system.domain.SysFileCategory;

public interface ISysFileCategoryService {
    SysFileCategory selectSysFileCategoryById(Long categoryId);

    List<SysFileCategory> selectSysFileCategoryList(SysFileCategory sysFileCategory);

    int insertSysFileCategory(SysFileCategory sysFileCategory);

    int updateSysFileCategory(SysFileCategory sysFileCategory);

    int deleteSysFileCategoryByIds(Long[] categoryIds);

    int deleteSysFileCategoryById(Long categoryId);
}
