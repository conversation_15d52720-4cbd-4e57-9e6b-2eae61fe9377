package com.ruoyi.system.service;

import com.ruoyi.system.domain.SysFileInfo;

import java.util.List;

public interface ISysFileInfoService {

    SysFileInfo selectSysFileInfoById(Long fileId);

    SysFileInfo selectSysFileInfoByName(String fileName);

    List<SysFileInfo> selectSysFileInfoList(SysFileInfo sysFileInfo);

    int insertSysFileInfo(SysFileInfo sysFileInfo);

    int updateSysFileInfo(SysFileInfo sysFileInfo);

    int deleteSysFileInfoById(Long fileId);

    int deleteSysFileInfoByIds(Long[] fileIds);
}
