package com.ruoyi.system.service;

import java.util.List;
import com.ruoyi.system.domain.UserexcelInfo;

/**
 * 【请填写功能名称】Service接口
 *
 * <AUTHOR>
 * @date 2025-03-24
 */
public interface IUserexcelInfoService
{
    /**
     * 查询【请填写功能名称】
     *
     * @param userexcelId 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    public UserexcelInfo selectUserexcelInfoByUserexcelId(Long userexcelId);

    /**
     * 查询【请填写功能名称】列表
     *
     * @param userexcelInfo 【请填写功能名称】
     * @return 【请填写功能名称】集合
     */
    public List<UserexcelInfo> selectUserexcelInfoList(UserexcelInfo userexcelInfo);

    /**
     * 新增【请填写功能名称】
     *
     * @param userexcelInfo 【请填写功能名称】
     * @return 结果
     */
    public int insertUserexcelInfo(UserexcelInfo userexcelInfo);

    /**
     * 修改【请填写功能名称】
     *
     * @param userexcelInfo 【请填写功能名称】
     * @return 结果
     */
    public int updateUserexcelInfo(UserexcelInfo userexcelInfo);

    /**
     * 批量删除【请填写功能名称】
     *
     * @param userexcelIds 需要删除的【请填写功能名称】主键集合
     * @return 结果
     */
    public int deleteUserexcelInfoByUserexcelIds(Long[] userexcelIds);

    /**
     * 删除【请填写功能名称】信息
     *
     * @param userexcelId 【请填写功能名称】主键
     * @return 结果
     */
    public int deleteUserexcelInfoByUserexcelId(Long userexcelId);
}
