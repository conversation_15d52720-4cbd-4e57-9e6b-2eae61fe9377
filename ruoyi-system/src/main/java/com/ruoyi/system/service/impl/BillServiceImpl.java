package com.ruoyi.system.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.BillMapper;
import com.ruoyi.system.domain.Bill;
import com.ruoyi.system.service.IBillService;

/**
 * 账单Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-03-31
 */
@Service
public class BillServiceImpl implements IBillService
{
    @Autowired
    private BillMapper billMapper;

    /**
     * 查询账单
     *
     * @param billId 账单主键
     * @return 账单
     */
    @Override
    public Bill selectBillByBillId(Long billId)
    {
        return billMapper.selectBillByBillId(billId);
    }

    /**
     * 查询账单列表
     *
     * @param bill 账单
     * @return 账单
     */
    @Override
    public List<Bill> selectBillList(Bill bill)
    {
        return billMapper.selectBillList(bill);
    }

    /**
     * 新增账单
     *
     * @param bill 账单
     * @return 结果
     */
    @Override
    public int insertBill(Bill bill)
    {
        return billMapper.insertBill(bill);
    }

    /**
     * 修改账单
     *
     * @param bill 账单
     * @return 结果
     */
    @Override
    public int updateBill(Bill bill)
    {
        return billMapper.updateBill(bill);
    }

    @Override
    public int updateBillImg(Bill bill)
    {
        return billMapper.updateBillImg(bill);
    }

    /**
     * 批量删除账单
     *
     * @param billIds 需要删除的账单主键
     * @return 结果
     */
    @Override
    public int deleteBillByBillIds(Long[] billIds)
    {
        return billMapper.deleteBillByBillIds(billIds);
    }

    /**
     * 删除账单信息
     *
     * @param billId 账单主键
     * @return 结果
     */
    @Override
    public int deleteBillByBillId(Long billId)
    {
        return billMapper.deleteBillByBillId(billId);
    }
}
