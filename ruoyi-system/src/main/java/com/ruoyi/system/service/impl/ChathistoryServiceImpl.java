package com.ruoyi.system.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.ChathistoryMapper;
import com.ruoyi.system.domain.Chathistory;
import com.ruoyi.system.service.IChathistoryService;

/**
 * 聊天记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-02-28
 */
@Service
public class ChathistoryServiceImpl implements IChathistoryService
{
    @Autowired
    private ChathistoryMapper chathistoryMapper;

    /**
     * 查询聊天记录
     *
     * @param chatId 聊天记录主键
     * @return 聊天记录
     */

    @Override
    public Chathistory selectChathistoryByChatId(Long chatId)
    {
        return chathistoryMapper.selectChathistoryByChatId(chatId);
    }

    @Override
    public List<Chathistory> selectTreedayList(Long userId)
    {
        return chathistoryMapper.selectTreedayList(userId);
    }

    /**
     * 查询聊天记录列表
     *
     * @param chathistory 聊天记录
     * @return 聊天记录
     */
    @Override
    public List<Chathistory> selectChathistoryList(Chathistory chathistory)
    {
        return chathistoryMapper.selectChathistoryList(chathistory);
    }

    /**
     * 新增聊天记录
     *
     * @param chathistory 聊天记录
     * @return 结果
     */
    @Override
    public int insertChathistory(Chathistory chathistory)
    {
        return chathistoryMapper.insertChathistory(chathistory);
    }

    /**
     * 修改聊天记录
     *
     * @param chathistory 聊天记录
     * @return 结果
     */
    @Override
    public int updateChathistory(Chathistory chathistory)
    {
        return chathistoryMapper.updateChathistory(chathistory);
    }

    /**
     * 批量删除聊天记录
     *
     * @param chatIds 需要删除的聊天记录主键
     * @return 结果
     */
    @Override
    public int deleteChathistoryByChatIds(Long[] chatIds)
    {
        return chathistoryMapper.deleteChathistoryByChatIds(chatIds);
    }

    /**
     * 删除聊天记录信息
     *
     * @param chatId 聊天记录主键
     * @return 结果
     */
    @Override
    public int deleteChathistoryByChatId(Long chatId)
    {
        return chathistoryMapper.deleteChathistoryByChatId(chatId);
    }
}
