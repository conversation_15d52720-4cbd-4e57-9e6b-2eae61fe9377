package com.ruoyi.system.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.ExcelDataMapper;
import com.ruoyi.system.domain.ExcelData;
import com.ruoyi.system.service.IExcelDataService;

/**
 * 【请填写功能名称】Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-03-12
 */
@Service
public class ExcelDataServiceImpl implements IExcelDataService
{
    @Autowired
    private ExcelDataMapper excelDataMapper;

    /**
     * 查询【请填写功能名称】
     *
     * @param id 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    @Override
    public ExcelData selectExcelDataById(Long id)
    {
        return excelDataMapper.selectExcelDataById(id);
    }

    /**
     * 查询【请填写功能名称】列表
     *
     * @param excelData 【请填写功能名称】
     * @return 【请填写功能名称】
     */
    @Override
    public List<ExcelData> selectExcelDataList(ExcelData excelData)
    {
        return excelDataMapper.selectExcelDataList(excelData);
    }

    /**
     * 新增【请填写功能名称】
     *
     * @param excelData 【请填写功能名称】
     * @return 结果
     */
    @Override
    public int insertExcelData(ExcelData excelData)
    {
        return excelDataMapper.insertExcelData(excelData);
    }

    @Override
    public void saveData(List<ExcelData> excelData) {
        for (ExcelData data : excelData) {
            insertExcelData(data);
        }
    }

    /**
     * 修改【请填写功能名称】
     *
     * @param excelData 【请填写功能名称】
     * @return 结果
     */
    @Override
    public int updateExcelData(ExcelData excelData)
    {
        return excelDataMapper.updateExcelData(excelData);
    }

    /**
     * 批量删除【请填写功能名称】
     *
     * @param ids 需要删除的【请填写功能名称】主键
     * @return 结果
     */
    @Override
    public int deleteExcelDataByIds(Long[] ids)
    {
        return excelDataMapper.deleteExcelDataByIds(ids);
    }

    /**
     * 删除【请填写功能名称】信息
     *
     * @param id 【请填写功能名称】主键
     * @return 结果
     */
    @Override
    public int deleteExcelDataById(Long id)
    {
        return excelDataMapper.deleteExcelDataById(id);
    }
}
