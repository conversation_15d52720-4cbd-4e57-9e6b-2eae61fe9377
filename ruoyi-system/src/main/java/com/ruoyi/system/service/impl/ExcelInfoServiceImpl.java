package com.ruoyi.system.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.ExcelInfoMapper;
import com.ruoyi.system.domain.ExcelInfo;
import com.ruoyi.system.service.IExcelInfoService;

/**
 * 【请填写功能名称】Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-03-19
 */
@Service
public class ExcelInfoServiceImpl implements IExcelInfoService
{
    @Autowired
    private ExcelInfoMapper excelInfoMapper;

    /**
     * 查询【请填写功能名称】
     *
     * @param infoId 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    @Override
    public ExcelInfo selectExcelInfoByInfoId(Long infoId)
    {
        return excelInfoMapper.selectExcelInfoByInfoId(infoId);
    }

    /**
     * 查询【请填写功能名称】列表
     *
     * @param excelInfo 【请填写功能名称】
     * @return 【请填写功能名称】
     */
    @Override
    public List<ExcelInfo> selectExcelInfoList(ExcelInfo excelInfo)
    {
        return excelInfoMapper.selectExcelInfoList(excelInfo);
    }

    /**
     * 新增【请填写功能名称】
     *
     * @param excelInfo 【请填写功能名称】
     * @return 结果
     */
    @Override
    public int insertExcelInfo(ExcelInfo excelInfo)
    {
        return excelInfoMapper.insertExcelInfo(excelInfo);
    }

    /**
     * 修改【请填写功能名称】
     *
     * @param excelInfo 【请填写功能名称】
     * @return 结果
     */
    @Override
    public int updateExcelInfo(ExcelInfo excelInfo)
    {
        return excelInfoMapper.updateExcelInfo(excelInfo);
    }

    /**
     * 批量删除【请填写功能名称】
     *
     * @param infoIds 需要删除的【请填写功能名称】主键
     * @return 结果
     */
    @Override
    public int deleteExcelInfoByInfoIds(Long[] infoIds)
    {
        return excelInfoMapper.deleteExcelInfoByInfoIds(infoIds);
    }

    /**
     * 删除【请填写功能名称】信息
     *
     * @param infoId 【请填写功能名称】主键
     * @return 结果
     */
    @Override
    public int deleteExcelInfoByInfoId(Long infoId)
    {
        return excelInfoMapper.deleteExcelInfoByInfoId(infoId);
    }
}
