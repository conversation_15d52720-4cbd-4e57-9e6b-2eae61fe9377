package com.ruoyi.system.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.IntroductionjobMapper;
import com.ruoyi.system.domain.Introductionjob;
import com.ruoyi.system.service.IIntroductionjobService;

/**
 * 项目介绍信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-02-13
 */
@Service
public class IntroductionjobServiceImpl implements IIntroductionjobService
{
    @Autowired
    private IntroductionjobMapper introductionjobMapper;

    /**
     * 查询项目介绍信息
     *
     * @param intrId 项目介绍信息主键
     * @return 项目介绍信息
     */
    @Override
    public Introductionjob selectIntroductionjobByIntrId(Long intrId)
    {
        return introductionjobMapper.selectIntroductionjobByIntrId(intrId);
    }

    /**
     * 查询项目介绍信息列表
     *
     * @param introductionjob 项目介绍信息
     * @return 项目介绍信息
     */
    @Override
    public List<Introductionjob> selectIntroductionjobList(Introductionjob introductionjob)
    {
        return introductionjobMapper.selectIntroductionjobList(introductionjob);
    }

    /**
     * 新增项目介绍信息
     *
     * @param introductionjob 项目介绍信息
     * @return 结果
     */
    @Override
    public int insertIntroductionjob(Introductionjob introductionjob)
    {
        System.out.println("ImageData="+introductionjob.getImageData());
        return introductionjobMapper.insertIntroductionjob(introductionjob);
    }

    /**
     * 修改项目介绍信息
     *
     * @param introductionjob 项目介绍信息
     * @return 结果
     */
    @Override
    public int updateIntroductionjob(Introductionjob introductionjob)
    {
        System.out.println("update ImageData="+introductionjob.getImageData());
        return introductionjobMapper.updateIntroductionjob(introductionjob);
    }

    /**
     * 批量删除项目介绍信息
     *
     * @param intrIds 需要删除的项目介绍信息主键
     * @return 结果
     */
    @Override
    public int deleteIntroductionjobByIntrIds(Long[] intrIds)
    {
        return introductionjobMapper.deleteIntroductionjobByIntrIds(intrIds);
    }

    /**
     * 删除项目介绍信息信息
     *
     * @param intrId 项目介绍信息主键
     * @return 结果
     */
    @Override
    public int deleteIntroductionjobByIntrId(Long intrId)
    {
        return introductionjobMapper.deleteIntroductionjobByIntrId(intrId);
    }
}
