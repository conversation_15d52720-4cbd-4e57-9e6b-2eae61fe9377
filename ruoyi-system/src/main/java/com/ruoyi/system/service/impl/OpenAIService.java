package com.ruoyi.system.service.impl;

import com.ruoyi.system.domain.OpenAIConfig;
import com.theokanning.openai.service.OpenAiService;
import com.theokanning.openai.completion.CompletionRequest;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Service
public class OpenAIService {

    private OpenAiService openAiService;

    public OpenAIService() {
        this.openAiService = OpenAIConfig.getOpenAiService();
    }

    public String getCompletion(String prompt) {
        try {
            CompletionRequest completionRequest = CompletionRequest.builder()
                    .prompt(prompt)
                    .model("text-davinci-003")
                    .maxTokens(150)
                    .temperature(0.7)
                    .build();

            return openAiService.createCompletion(completionRequest).getChoices().get(0).getText();
        }
        catch (Exception ex)
        {
            //ex.printStackTrace();‘
            System.out.println(ex.getMessage());
            return "到error了？";
        }
    }
}
