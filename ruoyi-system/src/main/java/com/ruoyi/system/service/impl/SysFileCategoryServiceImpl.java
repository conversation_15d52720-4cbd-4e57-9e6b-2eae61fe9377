package com.ruoyi.system.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.SysFileCategoryMapper;
import com.ruoyi.system.domain.SysFileCategory;
import com.ruoyi.system.service.ISysFileCategoryService;

@Service
public class SysFileCategoryServiceImpl implements ISysFileCategoryService {
    @Autowired
    private SysFileCategoryMapper sysFileCategoryMapper;

    @Override
    public SysFileCategory selectSysFileCategoryById(Long categoryId) {
        return sysFileCategoryMapper.selectSysFileCategoryById(categoryId);
    }

    @Override
    public List<SysFileCategory> selectSysFileCategoryList(SysFileCategory sysFileCategory) {
        return sysFileCategoryMapper.selectSysFileCategoryList(sysFileCategory);
    }

    @Override
    public int insertSysFileCategory(SysFileCategory sysFileCategory) {
        return sysFileCategoryMapper.insertSysFileCategory(sysFileCategory);
    }

    @Override
    public int updateSysFileCategory(SysFileCategory sysFileCategory) {
        return sysFileCategoryMapper.updateSysFileCategory(sysFileCategory);
    }

    @Override
    public int deleteSysFileCategoryByIds(Long[] categoryIds) {
        return sysFileCategoryMapper.deleteSysFileCategoryByIds(categoryIds);
    }

    @Override
    public int deleteSysFileCategoryById(Long categoryId) {
        return sysFileCategoryMapper.deleteSysFileCategoryById(categoryId);
    }
}
