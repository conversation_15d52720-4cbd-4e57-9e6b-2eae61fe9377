package com.ruoyi.system.service.impl;

import com.ruoyi.system.domain.SysFileInfo;
import com.ruoyi.system.mapper.SysFileInfoMapper;
import com.ruoyi.system.service.ISysFileInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class SysFileInfoServiceImpl implements ISysFileInfoService {

    @Autowired
    private SysFileInfoMapper sysFileInfoMapper;

    @Override
    public SysFileInfo selectSysFileInfoById(Long fileId) {
        return sysFileInfoMapper.selectSysFileInfoById(fileId);
    }

    @Override
    public SysFileInfo selectSysFileInfoByName(String fileName) {
        return sysFileInfoMapper.selectSysFileInfoByName(fileName);
    }

    @Override
    public List<SysFileInfo> selectSysFileInfoList(SysFileInfo sysFileInfo) {
        return sysFileInfoMapper.selectSysFileInfoList(sysFileInfo);
    }

    @Override
    public int insertSysFileInfo(SysFileInfo sysFileInfo) {
        return sysFileInfoMapper.insertSysFileInfo(sysFileInfo);
    }

    @Override
    public int updateSysFileInfo(SysFileInfo sysFileInfo) {
        return sysFileInfoMapper.updateSysFileInfo(sysFileInfo);
    }

    @Override
    public int deleteSysFileInfoById(Long fileId) {
        return sysFileInfoMapper.deleteSysFileInfoById(fileId);
    }

    @Override
    public int deleteSysFileInfoByIds(Long[] fileIds) {
        return sysFileInfoMapper.deleteSysFileInfoByIds(fileIds);
    }
}
