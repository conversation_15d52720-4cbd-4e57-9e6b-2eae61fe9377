package com.ruoyi.system.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.UserexcelInfoMapper;
import com.ruoyi.system.domain.UserexcelInfo;
import com.ruoyi.system.service.IUserexcelInfoService;

/**
 * 【请填写功能名称】Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-03-24
 */
@Service
public class UserexcelInfoServiceImpl implements IUserexcelInfoService
{
    @Autowired
    private UserexcelInfoMapper userexcelInfoMapper;

    /**
     * 查询【请填写功能名称】
     *
     * @param userexcelId 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    @Override
    public UserexcelInfo selectUserexcelInfoByUserexcelId(Long userexcelId)
    {
        return userexcelInfoMapper.selectUserexcelInfoByUserexcelId(userexcelId);
    }

    /**
     * 查询【请填写功能名称】列表
     *
     * @param userexcelInfo 【请填写功能名称】
     * @return 【请填写功能名称】
     */
    @Override
    public List<UserexcelInfo> selectUserexcelInfoList(UserexcelInfo userexcelInfo)
    {
        List<UserexcelInfo> list=userexcelInfoMapper.selectUserexcelInfoList(userexcelInfo);
        return list;
    }

    /**
     * 新增【请填写功能名称】
     *
     * @param userexcelInfo 【请填写功能名称】
     * @return 结果
     */
    @Override
    public int insertUserexcelInfo(UserexcelInfo userexcelInfo)
    {
        return userexcelInfoMapper.insertUserexcelInfo(userexcelInfo);
    }

    /**
     * 修改【请填写功能名称】
     *
     * @param userexcelInfo 【请填写功能名称】
     * @return 结果
     */
    @Override
    public int updateUserexcelInfo(UserexcelInfo userexcelInfo)
    {
        return userexcelInfoMapper.updateUserexcelInfo(userexcelInfo);
    }

    /**
     * 批量删除【请填写功能名称】
     *
     * @param userexcelIds 需要删除的【请填写功能名称】主键
     * @return 结果
     */
    @Override
    public int deleteUserexcelInfoByUserexcelIds(Long[] userexcelIds)
    {
        return userexcelInfoMapper.deleteUserexcelInfoByUserexcelIds(userexcelIds);
    }

    /**
     * 删除【请填写功能名称】信息
     *
     * @param userexcelId 【请填写功能名称】主键
     * @return 结果
     */
    @Override
    public int deleteUserexcelInfoByUserexcelId(Long userexcelId)
    {
        return userexcelInfoMapper.deleteUserexcelInfoByUserexcelId(userexcelId);
    }
}
