<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.BillMapper">

    <resultMap type="Bill" id="BillResult">
        <result property="billId"    column="bill_id"    />
        <result property="billDate"    column="bill_date"    />
        <result property="voucherNo"    column="voucher_no"    />
        <result property="billDirect"    column="bill_direct"    />
        <result property="firstlevel"    column="firstlevel"    />
        <result property="secondlevel"    column="secondlevel"    />
        <result property="thirdlevel"    column="thirdlevel"    />
        <result property="accountproject"    column="accountproject"    />
        <result property="occurretamount"    column="occurretamount"    />
        <result property="balanceDirect"    column="balance_direct"    />
        <result property="balance"    column="balance"    />
        <result property="notes"    column="notes"    />
        <result property="bak1"    column="bak1"    />
        <result property="bak2"    column="bak2"    />
        <result property="bak3"    column="bak3"    />
        <result property="bak4"    column="bak4"    />
        <result property="bak5"    column="bak5"    />
        <result property="doc1"    column="doc1"    />
        <result property="doc2"    column="doc2"    />
        <result property="doc3"    column="doc3"    />
        <result property="doc4"    column="doc4"    />
        <result property="doc5"    column="doc5"    />
    </resultMap>

    <sql id="selectBillVo">
        select bill_id, bill_date, voucher_no, bill_direct, firstlevel, secondlevel, thirdlevel, accountproject, occurretamount, balance_direct, balance, notes, bak1, bak2, bak3, bak4, bak5, doc1, doc2, doc3, doc4, doc5 from bill
    </sql>

    <select id="selectBillList" parameterType="Bill" resultMap="BillResult">
        <include refid="selectBillVo"/>
        <where>
            <if test="billDate != null  and billDate != ''"> and bill_date = #{billDate}</if>
            <if test="voucherNo != null  and voucherNo != ''"> and voucher_no = #{voucherNo}</if>
            <if test="billDirect != null  and billDirect != ''"> and bill_direct = #{billDirect}</if>
            <if test="firstlevel != null  and firstlevel != ''"> and firstlevel = #{firstlevel}</if>
            <if test="secondlevel != null  and secondlevel != ''"> and secondlevel = #{secondlevel}</if>
            <if test="thirdlevel != null  and thirdlevel != ''"> and thirdlevel = #{thirdlevel}</if>
            <if test="accountproject != null  and accountproject != ''"> and accountproject = #{accountproject}</if>
            <if test="occurretamount != null  and occurretamount != ''"> and occurretamount = #{occurretamount}</if>
            <if test="balanceDirect != null  and balanceDirect != ''"> and balance_direct = #{balanceDirect}</if>
            <if test="balance != null  and balance != ''"> and balance = #{balance}</if>
            <if test="notes != null  and notes != ''"> and notes = #{notes}</if>
            <if test="bak1 != null  and bak1 != ''"> and bak1 = #{bak1}</if>
            <if test="bak2 != null  and bak2 != ''"> and bak2 = #{bak2}</if>
            <if test="bak3 != null  and bak3 != ''"> and bak3 = #{bak3}</if>
            <if test="bak4 != null  and bak4 != ''"> and bak4 = #{bak4}</if>
            <if test="bak5 != null  and bak5 != ''"> and bak5 = #{bak5}</if>
            <if test="doc1 != null  and doc1 != ''"> and doc1 = #{doc1}</if>
            <if test="doc2 != null  and doc2 != ''"> and doc2 = #{doc2}</if>
            <if test="doc3 != null  and doc3 != ''"> and doc3 = #{doc3}</if>
            <if test="doc4 != null  and doc4 != ''"> and doc4 = #{doc4}</if>
            <if test="doc5 != null  and doc5 != ''"> and doc5 = #{doc5}</if>
        </where>
    </select>

    <select id="selectBillByBillId" parameterType="Long" resultMap="BillResult">
        <include refid="selectBillVo"/>
        where bill_id = #{billId}
    </select>

    <insert id="insertBill" parameterType="Bill" useGeneratedKeys="true" keyProperty="billId">
        insert into bill
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="billDate != null">bill_date,</if>
            <if test="voucherNo != null">voucher_no,</if>
            <if test="billDirect != null">bill_direct,</if>
            <if test="firstlevel != null">firstlevel,</if>
            <if test="secondlevel != null">secondlevel,</if>
            <if test="thirdlevel != null">thirdlevel,</if>
            <if test="accountproject != null">accountproject,</if>
            <if test="occurretamount != null">occurretamount,</if>
            <if test="balanceDirect != null">balance_direct,</if>
            <if test="balance != null">balance,</if>
            <if test="notes != null">notes,</if>
            <if test="bak1 != null">bak1,</if>
            <if test="bak2 != null">bak2,</if>
            <if test="bak3 != null">bak3,</if>
            <if test="bak4 != null">bak4,</if>
            <if test="bak5 != null">bak5,</if>
            <if test="doc1 != null">doc1,</if>
            <if test="doc2 != null">doc2,</if>
            <if test="doc3 != null">doc3,</if>
            <if test="doc4 != null">doc4,</if>
            <if test="doc5 != null">doc5,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="billDate != null">#{billDate},</if>
            <if test="voucherNo != null">#{voucherNo},</if>
            <if test="billDirect != null">#{billDirect},</if>
            <if test="firstlevel != null">#{firstlevel},</if>
            <if test="secondlevel != null">#{secondlevel},</if>
            <if test="thirdlevel != null">#{thirdlevel},</if>
            <if test="accountproject != null">#{accountproject},</if>
            <if test="occurretamount != null">#{occurretamount},</if>
            <if test="balanceDirect != null">#{balanceDirect},</if>
            <if test="balance != null">#{balance},</if>
            <if test="notes != null">#{notes},</if>
            <if test="bak1 != null">#{bak1},</if>
            <if test="bak2 != null">#{bak2},</if>
            <if test="bak3 != null">#{bak3},</if>
            <if test="bak4 != null">#{bak4},</if>
            <if test="bak5 != null">#{bak5},</if>
            <if test="doc1 != null">#{doc1},</if>
            <if test="doc2 != null">#{doc2},</if>
            <if test="doc3 != null">#{doc3},</if>
            <if test="doc4 != null">#{doc4},</if>
            <if test="doc5 != null">#{doc5},</if>
        </trim>
    </insert>

    <update id="updateBill" parameterType="Bill">
        update bill
        <trim prefix="SET" suffixOverrides=",">
            <if test="billDate != null">bill_date = #{billDate},</if>
            <if test="voucherNo != null">voucher_no = #{voucherNo},</if>
            <if test="billDirect != null">bill_direct = #{billDirect},</if>
            <if test="firstlevel != null">firstlevel = #{firstlevel},</if>
            <if test="secondlevel != null">secondlevel = #{secondlevel},</if>
            <if test="thirdlevel != null">thirdlevel = #{thirdlevel},</if>
            <if test="accountproject != null">accountproject = #{accountproject},</if>
            <if test="occurretamount != null">occurretamount = #{occurretamount},</if>
            <if test="balanceDirect != null">balance_direct = #{balanceDirect},</if>
            <if test="balance != null">balance = #{balance},</if>
            <if test="notes != null">notes = #{notes},</if>
            <if test="bak1 != null">bak1 = #{bak1},</if>
            <if test="bak2 != null">bak2 = #{bak2},</if>
            <if test="bak3 != null">bak3 = #{bak3},</if>
            <if test="bak4 != null">bak4 = #{bak4},</if>
            <if test="bak5 != null">bak5 = #{bak5},</if>
            <if test="doc1 != null">doc1 = #{doc1},</if>
            <if test="doc2 != null">doc2 = #{doc2},</if>
            <if test="doc3 != null">doc3 = #{doc3},</if>
            <if test="doc4 != null">doc4 = #{doc4},</if>
            <if test="doc5 != null">doc5 = #{doc5},</if>
        </trim>
        where bill_id = #{billId}
    </update>

    <update id="updateBillImg" parameterType="Bill">
        update bill
        <trim prefix="SET" suffixOverrides=",">

            <if test="bak1 != null">bak1 = #{bak1},</if>
            <if test="bak2 != null">bak2 = #{bak2},</if>
            <if test="bak3 != null">bak3 = #{bak3},</if>
            <if test="bak4 != null">bak4 = #{bak4},</if>
            <if test="bak5 != null">bak5 = #{bak5},</if>

        </trim>
        where bill_id = #{billId}
    </update>

    <delete id="deleteBillByBillId" parameterType="Long">
        delete from bill where bill_id = #{billId}
    </delete>

    <delete id="deleteBillByBillIds" parameterType="String">
        delete from bill where bill_id in
        <foreach item="billId" collection="array" open="(" separator="," close=")">
            #{billId}
        </foreach>
    </delete>
</mapper>