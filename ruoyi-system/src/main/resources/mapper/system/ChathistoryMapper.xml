<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.ChathistoryMapper">

    <resultMap type="Chathistory" id="ChathistoryResult">
        <result property="chatId"    column="chat_id"    />
        <result property="userId"    column="user_id"    />
        <result property="chatTile"    column="chat_tile"    />
        <result property="chatContent"    column="chat_content"    />
        <result property="chatTime"    column="chat_time"    />
        <result property="chatRemark"    column="chat_remark"    />
    </resultMap>

    <sql id="selectChathistoryVo">
        select chat_id, user_id, chat_tile, chat_content, chat_time, chat_remark from chathistory
    </sql>

    <select id="selectChathistoryList" parameterType="Chathistory" resultMap="ChathistoryResult">
        <include refid="selectChathistoryVo"/>
        <where>
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="chatTile != null  and chatTile != ''"> and chat_tile = #{chatTile}</if>
            <if test="chatContent != null  and chatContent != ''"> and chat_content = #{chatContent}</if>
            <if test="chatTime != null  and chatTime != ''"> and chat_time = #{chatTime}</if>
            <if test="chatRemark != null  and chatRemark != ''"> and chat_remark = #{chatRemark}</if>
        </where>
    </select>
    <select id="selectTreedayList" parameterType="Long" resultMap="ChathistoryResult">
        <include refid="selectChathistoryVo"/>
        <where>
            user_id=#{userId} order by chat_time desc
        </where>
    </select>

    <select id="selectChathistoryByChatId" parameterType="Long" resultMap="ChathistoryResult">
        <include refid="selectChathistoryVo"/>
        where chat_id = #{chatId}
    </select>

    <insert id="insertChathistory" parameterType="Chathistory" useGeneratedKeys="true" keyProperty="chatId">
        insert into chathistory
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="chatTile != null">chat_tile,</if>
            <if test="chatContent != null">chat_content,</if>
            <if test="chatTime != null">chat_time,</if>
            <if test="chatRemark != null">chat_remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="chatTile != null">#{chatTile},</if>
            <if test="chatContent != null">#{chatContent},</if>
            <if test="chatTime != null">#{chatTime},</if>
            <if test="chatRemark != null">#{chatRemark},</if>
        </trim>
    </insert>

    <update id="updateChathistory" parameterType="Chathistory">
        update chathistory
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="chatTile != null">chat_tile = #{chatTile},</if>
            <if test="chatContent != null">chat_content = #{chatContent},</if>
            <if test="chatTime != null">chat_time = #{chatTime},</if>
            <if test="chatRemark != null">chat_remark = #{chatRemark},</if>
        </trim>
        where chat_id = #{chatId}
    </update>

    <delete id="deleteChathistoryByChatId" parameterType="Long">
        delete from chathistory where chat_id = #{chatId}
    </delete>

    <delete id="deleteChathistoryByChatIds" parameterType="String">
        delete from chathistory where chat_id in
        <foreach item="chatId" collection="array" open="(" separator="," close=")">
            #{chatId}
        </foreach>
    </delete>
</mapper>