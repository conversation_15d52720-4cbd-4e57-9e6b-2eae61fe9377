<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ruoyi.system.mapper.CourseMapper">

    <resultMap id="CourseResult" type="com.ruoyi.system.domain.Course">
        <id column="course_id" property="courseId"/>
        <result column="title" property="title"/>
        <result column="subtitle" property="subtitle"/>
        <result column="teacher" property="teacher"/>
        <result column="teacher_avatar" property="teacherAvatar"/>
        <result column="date" property="date"/>
        <result column="students" property="students"/>
        <result column="status" property="status"/>
        <result column="category_id" property="categoryId"/>
        <result column="draft" property="draft"/>
        <result column="remark" property="remark"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <!-- 查询单个 -->
    <select id="selectCourseById" parameterType="Long" resultMap="CourseResult">
        SELECT * FROM course WHERE course_id = #{courseId}
    </select>

    <!-- 查询列表 -->
    <select id="selectCourseList" parameterType="com.ruoyi.system.domain.Course" resultMap="CourseResult">
        SELECT * FROM course
        <where>
            <if test="title != null and title != ''">
                AND title LIKE CONCAT('%', #{title}, '%')
            </if>
            <if test="teacher != null and teacher != ''">
                AND teacher LIKE CONCAT('%', #{teacher}, '%')
            </if>
            <if test="status != null and status != ''">
                AND status = #{status}
            </if>
            <if test="categoryId != null">
                AND category_id = #{categoryId}
            </if>
        </where>
        ORDER BY create_time DESC
    </select>

    <!-- 新增 -->
    <insert id="insertCourse" parameterType="com.ruoyi.system.domain.Course" useGeneratedKeys="true" keyProperty="courseId">
        INSERT INTO course
        (
        title, subtitle, teacher, teacher_avatar, date, students, status, category_id, draft, remark, create_by, create_time
        )
        VALUES
        (
        #{title}, #{subtitle}, #{teacher}, #{teacherAvatar}, #{date}, #{students}, #{status}, #{categoryId}, #{draft}, #{remark}, #{createBy}, NOW()
        )
    </insert>

    <!-- 修改 -->
    <update id="updateCourse" parameterType="com.ruoyi.system.domain.Course">
        UPDATE course
        SET
        title = #{title},
        subtitle = #{subtitle},
        teacher = #{teacher},
        teacher_avatar = #{teacherAvatar},
        date = #{date},
        students = #{students},
        status = #{status},
        category_id = #{categoryId},
        draft = #{draft},
        remark = #{remark},
        update_by = #{updateBy},
        update_time = NOW()
        WHERE course_id = #{courseId}
    </update>

    <!-- 删除单个 -->
    <delete id="deleteCourseById" parameterType="Long">
        DELETE FROM course WHERE course_id = #{courseId}
    </delete>

    <!-- 批量删除 -->
    <delete id="deleteCourseByIds" parameterType="Long">
        DELETE FROM course WHERE course_id IN
        <foreach collection="array" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>
