<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.ExcelDataMapper">

    <resultMap type="ExcelData" id="ExcelDataResult">
        <result property="id"    column="id"    />
        <result property="name"    column="name"    />
        <result property="value"    column="value"    />
        <result property="formula"    column="formula"    />
    </resultMap>

    <sql id="selectExcelDataVo">
        select id, name, value, formula from excel_data
    </sql>

    <select id="selectExcelDataList" parameterType="ExcelData" resultMap="ExcelDataResult">
        <include refid="selectExcelDataVo"/>
        <where>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="value != null "> and value = #{value}</if>
            <if test="formula != null  and formula != ''"> and formula = #{formula}</if>
        </where>
    </select>

    <select id="selectExcelDataById" parameterType="Long" resultMap="ExcelDataResult">
        <include refid="selectExcelDataVo"/>
        where id = #{id}
    </select>

    <insert id="insertExcelData" parameterType="ExcelData" useGeneratedKeys="true" keyProperty="id">
        insert into excel_data
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null">name,</if>
            <if test="value != null">value,</if>
            <if test="formula != null">formula,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null">#{name},</if>
            <if test="value != null">#{value},</if>
            <if test="formula != null">#{formula},</if>
        </trim>
    </insert>

    <update id="updateExcelData" parameterType="ExcelData">
        update excel_data
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null">name = #{name},</if>
            <if test="value != null">value = #{value},</if>
            <if test="formula != null">formula = #{formula},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteExcelDataById" parameterType="Long">
        delete from excel_data where id = #{id}
    </delete>

    <delete id="deleteExcelDataByIds" parameterType="String">
        delete from excel_data where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>