<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.ExcelInfoMapper">

    <resultMap type="ExcelInfo" id="ExcelInfoResult">
        <result property="infoId"    column="info_id"    />
        <result property="excelName"    column="excel_name"    />
        <result property="excelPath"    column="excel_path"    />
        <result property="excelRemark"    column="excel_remark"    />
    </resultMap>

    <sql id="selectExcelInfoVo">
        select info_id, excel_name, excel_path, excel_remark from excel_info
    </sql>

    <select id="selectExcelInfoList" parameterType="ExcelInfo" resultMap="ExcelInfoResult">
        <include refid="selectExcelInfoVo"/>
        <where>
            <if test="excelName != null  and excelName != ''"> and excel_name like concat('%', #{excelName}, '%')</if>
            <if test="excelPath != null  and excelPath != ''"> and excel_path = #{excelPath}</if>
            <if test="excelRemark != null  and excelRemark != ''"> and excel_remark = #{excelRemark}</if>
        </where>
    </select>

    <select id="selectExcelInfoByInfoId" parameterType="Long" resultMap="ExcelInfoResult">
        <include refid="selectExcelInfoVo"/>
        where info_id = #{infoId}
    </select>

    <insert id="insertExcelInfo" parameterType="ExcelInfo" useGeneratedKeys="true" keyProperty="infoId">
        insert into excel_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="excelName != null">excel_name,</if>
            <if test="excelPath != null">excel_path,</if>
            <if test="excelRemark != null">excel_remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="excelName != null">#{excelName},</if>
            <if test="excelPath != null">#{excelPath},</if>
            <if test="excelRemark != null">#{excelRemark},</if>
        </trim>
    </insert>

    <update id="updateExcelInfo" parameterType="ExcelInfo">
        update excel_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="excelName != null">excel_name = #{excelName},</if>
            <if test="excelPath != null">excel_path = #{excelPath},</if>
            <if test="excelRemark != null">excel_remark = #{excelRemark},</if>
        </trim>
        where info_id = #{infoId}
    </update>

    <delete id="deleteExcelInfoByInfoId" parameterType="Long">
        delete from excel_info where info_id = #{infoId}
    </delete>

    <delete id="deleteExcelInfoByInfoIds" parameterType="String">
        delete from excel_info where info_id in
        <foreach item="infoId" collection="array" open="(" separator="," close=")">
            #{infoId}
        </foreach>
    </delete>
</mapper>