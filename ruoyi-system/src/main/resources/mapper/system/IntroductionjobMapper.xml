<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.IntroductionjobMapper">

    <resultMap type="Introductionjob" id="IntroductionjobResult">
        <result property="intrId"    column="Intr_id"    />
        <result property="menuId"    column="menu_id"    />
        <result property="intrContent"    column="Intr_content"    />
        <result property="imageData"    column="image_data"    />
        <result property="intrRemark"    column="Intr_remark"    />
    </resultMap>

    <sql id="selectIntroductionjobVo">
        select Intr_id, menu_id, Intr_content, image_data, Intr_remark from introductionjob
    </sql>

    <select id="selectIntroductionjobList" parameterType="Introductionjob" resultMap="IntroductionjobResult">
        <include refid="selectIntroductionjobVo"/>
        <where>
            <if test="menuId != null "> and menu_id = #{menuId}</if>
            <!--<if test="intrContent != null  and intrContent != ''"> and Intr_content = #{intrContent}</if>-->
            <if test="intrRemark != null  and intrRemark != ''"> and Intr_remark like '%${intrRemark}%'</if>
        </where>
    </select>

    <select id="selectIntroductionjobByIntrId" parameterType="Long" resultMap="IntroductionjobResult">
        <include refid="selectIntroductionjobVo"/>
        where Intr_id = #{intrId}
    </select>

    <insert id="insertIntroductionjob" parameterType="Introductionjob" useGeneratedKeys="true" keyProperty="intrId">
        insert into introductionjob
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="menuId != null">menu_id,</if>
            <if test="intrContent != null and intrContent != ''">Intr_content,</if>
            <if test="imageData != null">image_data,</if>
            <if test="intrRemark != null">Intr_remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="menuId != null">#{menuId},</if>
            <if test="intrContent != null and intrContent != ''">#{intrContent},</if>
            <if test="imageData != null">#{imageData},</if>
            <if test="intrRemark != null">#{intrRemark},</if>
        </trim>
    </insert>

    <update id="updateIntroductionjob" parameterType="Introductionjob">
        update introductionjob
        <trim prefix="SET" suffixOverrides=",">
            <if test="menuId != null">menu_id = #{menuId},</if>
            <if test="intrContent != null and intrContent != ''">Intr_content = #{intrContent},</if>
            <if test="imageData != null">image_data = #{imageData},</if>
            <if test="intrRemark != null">Intr_remark = #{intrRemark},</if>
        </trim>
        where Intr_id = #{intrId}
    </update>

    <delete id="deleteIntroductionjobByIntrId" parameterType="Long">
        delete from introductionjob where Intr_id = #{intrId}
    </delete>

    <delete id="deleteIntroductionjobByIntrIds" parameterType="String">
        delete from introductionjob where Intr_id in
        <foreach item="intrId" collection="array" open="(" separator="," close=")">
            #{intrId}
        </foreach>
    </delete>
</mapper>