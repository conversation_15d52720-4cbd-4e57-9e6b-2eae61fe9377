<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ruoyi.system.mapper.SysFileCategoryMapper">
    <resultMap type="SysFileCategory" id="SysFileCategoryResult">
        <id column="category_id" property="categoryId"/>
        <result column="parent_id" property="parentId"/>
        <result column="ancestors" property="ancestors"/>
        <result column="category_name" property="categoryName"/>
        <result column="order_num" property="orderNum"/>
        <result column="status" property="status"/>
        <result column="del_flag" property="delFlag"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <select id="selectSysFileCategoryById" resultMap="SysFileCategoryResult">
        SELECT * FROM sys_file_category WHERE category_id = #{categoryId}
    </select>

    <select id="selectSysFileCategoryList" resultMap="SysFileCategoryResult">
        SELECT * FROM sys_file_category
    </select>

    <insert id="insertSysFileCategory">
        INSERT INTO sys_file_category (parent_id, ancestors, category_name, order_num, status, del_flag, create_by, create_time)
        VALUES (#{parentId}, #{ancestors}, #{categoryName}, #{orderNum}, #{status}, #{delFlag}, #{createBy}, NOW())
    </insert>

    <update id="updateSysFileCategory">
        UPDATE sys_file_category
        SET parent_id=#{parentId}, ancestors=#{ancestors}, category_name=#{categoryName},
        order_num=#{orderNum}, status=#{status}, del_flag=#{delFlag}, update_by=#{updateBy}, update_time=NOW()
        WHERE category_id=#{categoryId}
    </update>

    <delete id="deleteSysFileCategoryById">
        DELETE FROM sys_file_category WHERE category_id=#{categoryId}
    </delete>

    <delete id="deleteSysFileCategoryByIds">
        DELETE FROM sys_file_category WHERE category_id IN
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
