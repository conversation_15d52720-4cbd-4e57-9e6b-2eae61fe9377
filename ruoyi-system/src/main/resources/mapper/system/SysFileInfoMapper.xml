<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ruoyi.system.mapper.SysFileInfoMapper">

    <resultMap id="SysFileInfoResult" type="com.ruoyi.system.domain.SysFileInfo">
        <id     column="file_id"      property="fileId"/>
        <result column="category_id"  property="categoryId"/>
        <result column="file_name"    property="fileName"/>
        <result column="file_path"    property="filePath"/>
        <result column="file_url"     property="fileUrl"/>
        <result column="file_size"    property="fileSize"/>
        <result column="file_type"    property="fileType"/>
        <result column="status"       property="status"/>
        <result column="del_flag"     property="delFlag"/>
        <result column="create_by"    property="createBy"/>
        <result column="create_time"  property="createTime"/>
        <result column="update_by"    property="updateBy"/>
        <result column="update_time"  property="updateTime"/>
        <result column="remark"       property="remark"/>
    </resultMap>

    <!-- 根据ID查询 -->
    <select id="selectSysFileInfoById" resultMap="SysFileInfoResult">
        SELECT * FROM sys_file_info WHERE file_id = #{fileId}
    </select>

    <select id="selectSysFileInfoByName" parameterType="String" resultMap="SysFileInfoResult">
        SELECT * FROM sys_file_info WHERE file_name = #{fileId}
    </select>

    <!-- 列表查询 -->
    <select id="selectSysFileInfoList" resultMap="SysFileInfoResult">
        SELECT * FROM sys_file_info
        <where>
            <if test="fileName != null and fileName != ''">
                AND file_name LIKE CONCAT('%', #{fileName}, '%')
            </if>
            <if test="status != null and status != ''">
                AND status = #{status}
            </if>
            <if test="categoryId != null">
                AND category_id = #{categoryId}
            </if>
        </where>
        ORDER BY create_time DESC
    </select>

    <!-- 新增 -->
    <insert id="insertSysFileInfo" parameterType="com.ruoyi.system.domain.SysFileInfo">
        INSERT INTO sys_file_info (
        category_id, file_name, file_path, file_url, file_size,
        file_type, status, del_flag, create_by, create_time, remark
        )
        VALUES (
        #{categoryId}, #{fileName}, #{filePath}, #{fileUrl}, #{fileSize},
        #{fileType}, #{status}, #{delFlag}, #{createBy}, NOW(), #{remark}
        )
    </insert>

    <!-- 修改 -->
    <update id="updateSysFileInfo" parameterType="com.ruoyi.system.domain.SysFileInfo">
        UPDATE sys_file_info
        <set>
            <if test="categoryId != null">category_id = #{categoryId},</if>
            <if test="fileName != null">file_name = #{fileName},</if>
            <if test="filePath != null">file_path = #{filePath},</if>
            <if test="fileUrl != null">file_url = #{fileUrl},</if>
            <if test="fileSize != null">file_size = #{fileSize},</if>
            <if test="fileType != null">file_type = #{fileType},</if>
            <if test="status != null">status = #{status},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            update_time = NOW(),
            <if test="remark != null">remark = #{remark}</if>
        </set>
        WHERE file_id = #{fileId}
    </update>

    <!-- 删除单个 -->
    <delete id="deleteSysFileInfoById" parameterType="Long">
        DELETE FROM sys_file_info WHERE file_id = #{fileId}
    </delete>

    <!-- 批量删除 -->
    <delete id="deleteSysFileInfoByIds" parameterType="Long">
        DELETE FROM sys_file_info
        WHERE file_id IN
        <foreach collection="array" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>
