<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.UserexcelInfoMapper">

    <resultMap type="UserexcelInfo" id="UserexcelInfoResult">
        <result property="userexcelId"    column="userexcel_id"    />
        <result property="userId"    column="user_id"    />
        <result property="userName"    column="user_name"    />
        <result property="excelId"    column="excel_id"    />
        <result property="excelName"    column="excel_name"    />
        <result property="exceldocDate"    column="excelDoc_date"    />
        <result property="exceldocContent"    column="excelDoc_content"    />
        <result property="exceldocRemark"    column="excelDoc_remark"    />
    </resultMap>

    <sql id="selectUserexcelInfoVo">
        select userexcel_id, a.user_id,c.user_name, excel_id,b.excel_name, excelDoc_date, excelDoc_content, excelDoc_remark
        from userexcel_info a
        JOIN excel_info b ON a.excel_id = b.info_id
        JOIN sys_user c ON a.user_id = c.user_id
    </sql>

    <select id="selectUserexcelInfoList" parameterType="UserexcelInfo" resultMap="UserexcelInfoResult">
        <include refid="selectUserexcelInfoVo"/>
        <where>
            1=1
            <if test="userId != null "> and a.user_id = #{userId}</if>
            <if test="excelId != null "> and a.excel_id = #{excelId}</if>
            <if test="excelName != null "> and excel_name like concat('%', #{excelName}, '%')</if>
            <if test="exceldocDate != null  and exceldocDate != ''"> and excelDoc_date = #{exceldocDate}</if>
            <if test="exceldocContent != null  and exceldocContent != ''"> and excelDoc_content = #{exceldocContent}</if>
            <if test="exceldocRemark != null  and exceldocRemark != ''"> and excelDoc_remark = #{exceldocRemark}</if>
        </where>
        ORDER BY a.userexcel_id DESC
    </select>

    <select id="selectUserexcelInfoByUserexcelId" parameterType="Long" resultMap="UserexcelInfoResult">
        <include refid="selectUserexcelInfoVo"/>
        where userexcel_id = #{userexcelId}
    </select>

    <insert id="insertUserexcelInfo" parameterType="UserexcelInfo" useGeneratedKeys="true" keyProperty="userexcelId">
        insert into userexcel_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="excelId != null">excel_id,</if>
            <if test="exceldocDate != null">excelDoc_date,</if>
            <if test="exceldocContent != null">excelDoc_content,</if>
            <if test="exceldocRemark != null">excelDoc_remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="excelId != null">#{excelId},</if>
            <if test="exceldocDate != null">#{exceldocDate},</if>
            <if test="exceldocContent != null">#{exceldocContent},</if>
            <if test="exceldocRemark != null">#{exceldocRemark},</if>
        </trim>
    </insert>

    <update id="updateUserexcelInfo" parameterType="UserexcelInfo">
        update userexcel_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="excelId != null">excel_id = #{excelId},</if>
            <if test="exceldocDate != null">excelDoc_date = #{exceldocDate},</if>
            <if test="exceldocContent != null">excelDoc_content = #{exceldocContent},</if>
            <if test="exceldocRemark != null">excelDoc_remark = #{exceldocRemark},</if>
        </trim>
        where userexcel_id = #{userexcelId}
    </update>

    <delete id="deleteUserexcelInfoByUserexcelId" parameterType="Long">
        delete from userexcel_info where userexcel_id = #{userexcelId}
    </delete>

    <delete id="deleteUserexcelInfoByUserexcelIds" parameterType="String">
        delete from userexcel_info where userexcel_id in
        <foreach item="userexcelId" collection="array" open="(" separator="," close=")">
            #{userexcelId}
        </foreach>
    </delete>
</mapper>