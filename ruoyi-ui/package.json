{"name": "ruoyi", "version": "3.8.9", "description": "审计实训教学系统", "author": "", "license": "MIT", "scripts": {"dev": "vue-cli-service serve", "build:prod": "vue-cli-service build", "build:stage": "vue-cli-service build --mode staging", "preview": "node build/index.js --preview", "lint": "eslint --ext .js,.vue src"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"src/**/*.{js,vue}": ["eslint --fix", "git add"]}, "keywords": ["vue", "admin", "dashboard", "element-ui", "boilerplate", "admin-template", "management-system"], "repository": {"type": "git", "url": "https://gitee.com/y_project/RuoYi-Vue.git"}, "dependencies": {"@handsontable/vue": "^15.1.0", "@riophae/vue-treeselect": "0.4.0", "@univerjs/core": "^0.6.6", "@univerjs/design": "^0.6.4", "@univerjs/docs": "^0.6.4", "@univerjs/docs-ui": "^0.6.4", "@univerjs/engine-formula": "^0.6.4", "@univerjs/engine-render": "^0.6.6", "@univerjs/facade": "^0.5.5", "@univerjs/sheets": "^0.6.6", "@univerjs/sheets-formula": "^0.6.4", "@univerjs/sheets-ui": "^0.6.4", "@univerjs/ui": "^0.6.4", "@univerjs/webpack-plugin": "^0.5.1", "axios": "^0.28.1", "clipboard": "2.0.8", "core-js": "3.37.1", "echarts": "5.4.0", "element-ui": "2.15.14", "exceljs": "^4.4.0", "file-saver": "^2.0.5", "formula-parser": "^2.0.1", "fuse.js": "6.4.3", "handsontable": "^15.1.0", "highlight.js": "9.18.5", "hyperformula": "^1.3.1", "js-beautify": "1.13.0", "js-cookie": "3.0.1", "jsencrypt": "3.0.0-rc.1", "luckyexcel": "^1.0.1", "luckysheet": "^2.1.13", "nprogress": "0.2.0", "quill": "2.0.2", "screenfull": "5.0.2", "sheetjs-style": "^0.15.8", "showdown": "^2.1.0", "sortablejs": "1.10.2", "splitpanes": "2.4.1", "univer": "^0.0.5", "vue": "2.6.12", "vue-count-to": "1.0.13", "vue-cropper": "0.5.5", "vue-meta": "2.4.0", "vue-router": "3.4.9", "vuedraggable": "2.24.3", "vuex": "3.6.0", "ws": "^8.18.0", "xlsx": "^0.18.5", "xlsx-calc": "^0.9.2", "xlsx-populate": "^1.21.0", "xlsx-style": "^0.8.13"}, "devDependencies": {"@vue/cli-plugin-babel": "4.4.6", "@vue/cli-plugin-eslint": "4.4.6", "@vue/cli-service": "4.4.6", "babel-eslint": "10.1.0", "babel-plugin-dynamic-import-node": "2.3.3", "chalk": "4.1.0", "compression-webpack-plugin": "6.1.2", "connect": "3.6.6", "eslint": "7.15.0", "eslint-plugin-vue": "7.2.0", "lint-staged": "10.5.3", "runjs": "4.4.2", "sass": "1.32.13", "sass-loader": "10.1.1", "script-ext-html-webpack-plugin": "2.1.5", "svg-sprite-loader": "5.1.1", "vue-template-compiler": "2.6.12"}, "engines": {"node": ">=8.9", "npm": ">= 3.0.0"}, "browserslist": ["> 1%", "last 2 versions"]}