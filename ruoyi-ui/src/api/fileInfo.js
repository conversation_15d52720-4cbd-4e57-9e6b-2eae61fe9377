import request from "@/utils/request";

// 获取文件列表
export function listFileInfo(query) {
  return request({
    url: "/system/fileInfo/list",
    method: "get",
    params: query,
  });
}

// 删除文件
export function delFileInfo(fileId) {
  return request({
    url: "/system/fileInfo/" + fileId,
    method: "delete",
  });
}

// 保存文件信息（调用 /system/file POST）
export function addFileInfo(data) {
  return request({
    url: "/system/fileInfo",
    method: "post",
    data: data,
  });
}
