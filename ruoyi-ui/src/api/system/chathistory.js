import request from '@/utils/request'

// 查询聊天记录列表
export function listChathistory(query) {
  return request({
    url: '/system/chathistory/list',
    method: 'get',
    params: query
  })
}

// 查询聊天记录详细
export function getChathistory(chatId) {
  return request({
    url: '/system/chathistory/' + chatId,
    method: 'get'
  })
}

export function getTree(chatId) {
 return request({
    url: '/system/chathistory/tree/' + chatId,
    method: 'get'
  })
}

// 新增聊天记录
export function addChathistory(data) {
  return request({
    url: '/system/chathistory',
    method: 'post',
    data: data
  })
}

// 修改聊天记录
export function updateChathistory(data) {
  return request({
    url: '/system/chathistory',
    method: 'put',
    data: data
  })
}

// 删除聊天记录
export function delChathistory(chatId) {
  return request({
    url: '/system/chathistory/' + chatId,
    method: 'delete'
  })
}
