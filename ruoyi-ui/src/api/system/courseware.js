import request from '@/utils/request'
import axios from "axios";

export function handleFileUpload(file) {
     const formData = new FormData();
     formData.append("file", file);

     axios.post("/system/excel/upload", formData).then((res) => {
    if (res.data.code === 200) {
       this.processExcelData(res.data.data);
      } else {
       this.$message.error("Excel 解析失败");
       }
      });
   return false;
 }


