import request from '@/utils/request'

// 查询【请填写功能名称】列表
export function listInfo(query) {
  return request({
    url: '/system/info/list',
    method: 'get',
    params: query
  })
}

export function excelist(query) {
  return request({
    url: '/system/info/exceltree',
    method: 'get',
    params: query
  })
}

// 查询【请填写功能名称】详细
export function getInfo(infoId) {
  return request({
    url: '/system/info/' + infoId,
    method: 'get'
  })
}

// 新增【请填写功能名称】
export function addInfo(data) {
  return request({
    url: '/system/info',
    method: 'post',
    data: data
  })
}

// 修改【请填写功能名称】
export function updateInfo(data) {
  return request({
    url: '/system/info',
    method: 'put',
    data: data
  })
}

// 删除【请填写功能名称】
export function delInfo(infoId) {
  return request({
    url: '/system/info/' + infoId,
    method: 'delete'
  })
}

export function addExcelData(data) {
  return request({
    url: '/system/userexcel',
    method: 'post',
    data: data
  })
}

export function updateExcelData(data) {
  return request({
    url: '/system/userexcel',
    method: 'put',
    data: data
  })
}

export function getExcelInfo(infoId) {
   return request({
     url: '/system/userexcel/' + infoId,
     method: 'get'
})
}
