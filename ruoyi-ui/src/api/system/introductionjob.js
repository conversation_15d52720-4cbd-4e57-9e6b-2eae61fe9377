import request from '@/utils/request'

// 查询项目介绍信息列表
export function listIntroductionjob(query) {
  return request({
    url: '/system/introductionjob/list',
    method: 'get',
    params: query
  })
}

// 查询项目介绍信息详细
export function getIntroductionjob(intrId) {
  return request({
    url: '/system/introductionjob/' + intrId,
    method: 'get'
  })
}

// 新增项目介绍信息
export function addIntroductionjob(data) {
  return request({
    url: '/system/introductionjob',
    method: 'post',
    data: data
  })
}

export function uploadFile(data) {
  return request({
    url: '/system/uploadImage',
    method: 'post',
    data: data
  })
}

// 修改项目介绍信息
export function updateIntroductionjob(data) {
  return request({
    url: '/system/introductionjob',
    method: 'put',
    data: data
  })
}

// 删除项目介绍信息
export function delIntroductionjob(intrId) {
  return request({
    url: '/system/introductionjob/' + intrId,
    method: 'delete'
  })
}

export function completion(word) {
  return request({
    url: '/system/openai/completion?prompt='+word,
    method: 'get'
  })
}
