import request from '@/utils/request'

// 查询【请填写功能名称】列表
export function listInfo(query) {
  return request({
    url: '/system/userexcel/list',
    method: 'get',
    params: query
  })
}

// 查询【请填写功能名称】详细
export function getInfo(userexcelId) {
  return request({
    url: '/system/userexcel/' + userexcelId,
    method: 'get'
  })
}

// 新增【请填写功能名称】
export function addInfo(data) {
  return request({
    url: '/system/userexcel',
    method: 'post',
    data: data
  })
}

// 修改【请填写功能名称】
export function updateInfo(data) {
  return request({
    url: '/system/userexcel',
    method: 'put',
    data: data
  })
}

// 删除【请填写功能名称】
export function delInfo(userexcelId) {
  return request({
    url: '/system/userexcel/' + userexcelId,
    method: 'delete'
  })
}
