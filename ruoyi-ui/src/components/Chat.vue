<template>
  <div class="chat-container">
    <div class="messages">
      <div v-for="message in messages" :key="message.id" :class="{'sent': message.sent}">
        {{ message.text }}
      </div>
    </div>
    <div class="input-area">
      <input v-model="newMessage" @keyup.enter="sendMessage" placeholder="Type a message..." />
      <button @click="sendMessage">Send</button>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      messages: [],
      newMessage: ''
    };
  },
  methods: {
    sendMessage() {
      if (this.newMessage.trim() !== '') {
        const newMsg = { id: Date.now(), text: this.newMessage, sent: true }; // 假设所有发送的消息都是sent=true
        this.messages.push(newMsg);
        this.newMessage = ''; // 清空输入框
        // 模拟接收消息（实际应用中，这里应该是从WebSocket或API获取）
        setTimeout(() => {
          const receivedMsg = { id: Date.now() + 1, text: 'Hello there!', sent: false }; // 接收到的消息sent=false
          this.messages.push(receivedMsg);
        }, 1000);
      }
    }
  }
};
</script>

<style>
.chat-container {
  display: flex;
  flex-direction: column;
  height: 100%;
}
.messages {
  flex: 1;
  overflow-y: auto;
  padding: 10px;
}
.messages div {
  margin: 5px 0;
  padding: 10px;
  border-radius: 5px;
}
.messages .sent {
  background-color: #dcf8c6; /* Green background for sent messages */
  align-self: flex-end; /* Align sent messages to the right */
}
.input-area {
  display: flex;
}
.input-area input {
  flex: 1; /* Take the remaining space */
  padding: 10px;
}
</style>
