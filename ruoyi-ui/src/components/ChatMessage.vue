<template>
  <div :class="['message', sender]">
    <p>{{ message.text }}</p>
  </div>
</template>

<script>
export default {
  props: {
    message: {
      type: Object,
      required: true,
    },
  },
  computed: {
    sender() {
      return this.message.sender; // 根据发送者设置样式
    },
  },
};
</script>

<style scoped>
.message {
  margin-bottom: 10px;
  padding: 10px;
  border-radius: 5px;
  max-width: 80%;
}

.message.user {
  background-color: #007bff;
  color: white;
  margin-left: auto;
}

.message.ai {
  background-color: #f1f1f1;
  color: #333;
  margin-right: auto;
}
</style>
