<template>
  <div class="chat-app">
    <!-- 左侧树控件 -->
    <div class="left-side">
      <el-tree
        :data="treeData"
        :props="defaultProps"
        @node-click="download"
      ></el-tree>
      <div style="display: grid; margin-top: 10px;">
        <el-button type="primary" size="mini" @click="handleSave">保存</el-button>
      </div>
    </div>

    <!-- 右侧表格区域 -->
    <div class="right-side">
      <div ref="hotTable" class="handsontable-container"></div>
    </div>
  </div>
</template>

<script>
import Handsontable from "handsontable";
import "handsontable/dist/handsontable.full.css";
import ExcelJS from "exceljs";
import HyperFormula from "hyperformula";
import { listInfo, addExcelData, updateExcelData, excelist } from "@/api/system/info";
import axios from "axios";
import { getToken } from "@/utils/auth";

const baseURL = process.env.VUE_APP_BASE_API;

export default {
  data() {
    return {
      hotInstance: null,
      treeData: [],
      defaultProps: {
        children: "children",
        label: "label"
      },
      cellStyles: {},
      mergeCells: [],
      hyperformula: null,
      serverFiles: [],
      selectedFile: "",
      form: {},
      queryParams: {}
    };
  },

  created() {
    this.getList();
    this.makeTree();
    this.form = {
      userexcelId: null,
      userId: null,
      excelId: null,
      exceldocDate: null,
      exceldocContent: null,
      exceldocRemark: null
    };
  },

  methods: {
    makeTree() {
      excelist().then(response => {
        this.treeData = response;
      });
    },

    getList() {
      listInfo(this.queryParams).then(response => {
        this.serverFiles = response.rows.map(row => ({
          fileId: row.infoId,
          fileName: row.excelName
        }));
      });
    },

    async download(data) {
      const obj = JSON.parse(JSON.stringify(data));
      this.selectedFile = JSON.parse(obj.id);
      if (!this.selectedFile) {
        alert("请选择文件");
        return;
      }
      try {
        const arrayBuffer = await this.downloadFile();
        await this.loadExcelData(arrayBuffer);
      } catch (error) {
        console.error("下载失败:", error);
        alert("下载失败");
      }
    },

    async handleSave() {
      try {
        await this.$modal.confirm("确定要保存数据吗？");
        this.form.exceldocContent = JSON.stringify(this.hotInstance.getData());
        this.form.excelId = this.selectedFile;
        const date = new Date();
        this.form.exceldocDate = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, "0")}-${String(date.getDate()).padStart(2, "0")}`;
        this.form.userId = this.$store.state.user.id;

        if (this.form.userexcelId == null) {
          await addExcelData(this.form);
        } else {
          await updateExcelData(this.form);
        }
        this.$modal.msgSuccess("保存成功");
      } catch (error) {
        if (error !== "cancel") {
          console.error("保存失败:", error);
          this.$message.error("保存失败: " + (error.message || error));
        }
      }
    },

    async downloadFile() {
      const url = `${baseURL}/system/excel/download?id=${this.selectedFile}`;
      try {
        const response = await axios.get(url, {
          responseType: "arraybuffer",
          headers: { Authorization: `Bearer ${getToken()}` }
        });
        return response.data;
      } catch (error) {
        throw new Error("文件下载失败");
      }
    },

    async loadExcelData(arrayBuffer) {
      const workbook = new ExcelJS.Workbook();
      await workbook.xlsx.load(arrayBuffer);
      const worksheet = workbook.getWorksheet(1);

      const data = this.extractSheetData(worksheet);
      this.cellStyles = this.extractCellStyles(worksheet);
      this.mergeCells = this.extractMergeCells(worksheet);

      this.initHyperFormula();
      this.renderHandsontable(data);

      this.$nextTick(() => {
        this.replaceErrorsWithDefault();
      });
    },

    replaceErrorsWithDefault() {
      if (!this.hotInstance) return;

      const rowCount = this.hotInstance.countRows();
      const colCount = this.hotInstance.countCols();

      for (let row = 0; row < rowCount; row++) {
        for (let col = 0; col < colCount; col++) {
          const value = this.hotInstance.getDataAtCell(row, col);
          if (value === "#ERROR!") {
            this.hotInstance.setDataAtCell(row, col, "0.00");
          }
        }
      }
    },

    extractSheetData(worksheet) {
      const data = [];
      worksheet.eachRow({ includeEmpty: true }, row => {
        const rowData = [];
        row.eachCell({ includeEmpty: true }, cell => {
          let value = cell.value;

          if (cell.formula) {
            value = `=${cell.formula}`;
          } else if (value && typeof value === "object") {
            if (value.richText) {
              value = value.richText.map(rt => rt.text).join("");
            } else if (value.result !== undefined) {
              value = value.result;
            }
          }

          rowData.push(value || "");
        });
        data.push(rowData);
      });
      return data;
    },

    extractMergeCells(worksheet) {
      const mergeCells = [];
      worksheet.model.merges.forEach(merge => {
        const [start, end] = merge.split(":");
        const startCell = this.decodeCellAddress(start);
        const endCell = this.decodeCellAddress(end);

        mergeCells.push({
          row: startCell.row,
          col: startCell.col,
          rowspan: endCell.row - startCell.row + 1,
          colspan: endCell.col - startCell.col + 1
        });
      });
      return mergeCells;
    },

    extractCellStyles(worksheet) {
      const styles = {};
      worksheet.eachRow({ includeEmpty: true }, (row, rowIndex) => {
        row.eachCell({ includeEmpty: true }, (cell, colIndex) => {
          const address = this.encodeCellAddress(rowIndex - 1, colIndex - 1);
          const style = cell.style;

          styles[address] = {
            backgroundColor: style.fill?.fgColor?.argb ? `#${style.fill.fgColor.argb.slice(2)}` : null,
            fontColor: style.font?.color?.argb ? `#${style.font.color.argb.slice(2)}` : null,
            bold: style.font?.bold || false,
            italic: style.font?.italic || false,
            fontSize: style.font?.size || 12,
            textAlign: style.alignment?.horizontal || "left",
            border: this.extractBorderStyle(style.border)
          };
        });
      });

      return styles;
    },

    extractBorderStyle(border) {
      if (!border) return null;
      let style = {};
      if (border.left) style.borderLeft = "1px solid #000";
      if (border.right) style.borderRight = "1px solid #000";
      if (border.top) style.borderTop = "1px solid #000";
      if (border.bottom) style.borderBottom = "1px solid #000";
      return style;
    },

    decodeCellAddress(address) {
      const match = address.match(/([A-Z]+)(\d+)/);
      if (!match) return { row: 0, col: 0 };

      const colLetters = match[1];
      const rowNumber = parseInt(match[2], 10) - 1;
      let colNumber = 0;

      for (let i = 0; i < colLetters.length; i++) {
        colNumber = colNumber * 26 + (colLetters.charCodeAt(i) - 64);
      }

      return { row: rowNumber, col: colNumber - 1 };
    },

    encodeCellAddress(row, col) {
      let colLetters = "";
      col += 1;
      while (col > 0) {
        let remainder = (col - 1) % 26;
        colLetters = String.fromCharCode(65 + remainder) + colLetters;
        col = Math.floor((col - 1) / 26);
      }
      return `${colLetters}${row + 1}`;
    },

    initHyperFormula() {
      this.hyperformula = HyperFormula.buildEmpty({
        licenseKey: "internal-use-in-handsontable"
      });
    },

    renderHandsontable(data) {
      if (this.hotInstance) {
        this.hotInstance.destroy();
      }

      this.hotInstance = new Handsontable(this.$refs.hotTable, {
        data,
        rowHeaders: true,
        colHeaders: true,
        mergeCells: this.mergeCells,
        formulas: { engine: this.hyperformula },
        licenseKey: "non-commercial-and-evaluation", // 👈 加上了 licenseKey
        cells: (row, col) => {
          return { renderer: this.customRenderer };
        }
      });
    },

    customRenderer(instance, td, row, col, prop, value, cellProperties) {
      Handsontable.renderers.TextRenderer.apply(this, arguments);
      const address = this.encodeCellAddress(row, col);
      const style = this.cellStyles[address];

      if (style) {
        td.style.backgroundColor = style.backgroundColor || "";
        td.style.color = style.fontColor || "";
        td.style.fontWeight = style.bold ? "bold" : "";
        td.style.fontStyle = style.italic ? "italic" : "";
        td.style.textAlign = style.textAlign || "";
        Object.assign(td.style, style.border);
      }
    }
  }
};
</script>

<style>
.handsontable-container {
  width: 100%;
  height: 500px;
  overflow: auto;
}

.chat-app {
  display: flex;
  height: 100vh;
}

.left-side {
  width: 150px;
  border-right: 1px solid #ddd;
  padding: 10px;
  overflow-y: auto;
}

.right-side {
  flex: 1;
  display: flex;
  flex-direction: column;
}
</style>
