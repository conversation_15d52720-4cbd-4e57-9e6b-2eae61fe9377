<template>
  <div class="chat-app">
    <!-- 右侧表格区域 -->
    <div class="right-side">
      <div class="toolbar">
        <el-button type="primary" size="mini" @click="handleSave">保存</el-button>
        <el-button type="success" size="mini" @click="exportToExcel">导出 Excel</el-button>
      </div>
      <div ref="hotTable" class="handsontable-container"></div>
    </div>
  </div>
</template>

<script>
import Handsontable from "handsontable";
import "handsontable/dist/handsontable.full.css";
import ExcelJS from "exceljs";
import HyperFormula from "hyperformula";
import { addExcelData, updateExcelData, getExcelInfo } from "@/api/system/info";
import axios from "axios";
import { getToken } from "@/utils/auth";

const baseURL = process.env.VUE_APP_BASE_API;

export default {
  props: {
    id: {
      type: [String, Number],
      required: true
    }
  },

  data() {
    return {
      hotInstance: null,
      cellStyles: {},
      mergeCells: [],
      hyperformula: null,
      selectedFile: "",
      form: {},
    };
  },

  async mounted() {
    this.selectedFile = this.id;
    this.form = {
      userexcelId: null,
      userId: null,
      excelId: null,
      exceldocDate: null,
      exceldocContent: null,
      exceldocRemark: null
    };
    try {
      await this.getExcel();
    } catch (error) {
      console.error("加载失败:", error);
      this.$modal.msgError("加载数据失败");
    }
  },

  methods: {
    async getExcel() {
      const response = await getExcelInfo(this.selectedFile);
      this.form = { ...this.form, ...response.data };

      const arrayBuffer = await this.downloadFile(this.form.excelId);
      const savedData = JSON.parse(this.form.exceldocContent);
      await this.loadExcelData(arrayBuffer, savedData);
    },

    async downloadFile(excelId) {
      const url = `${baseURL}/system/excel/download?id=${excelId}`;
      const response = await axios.get(url, {
        responseType: "arraybuffer",
        headers: { Authorization: `Bearer ${getToken()}` }
      });
      return response.data;
    },

    async handleSave() {
      try {
        await this.$modal.confirm("确定要保存数据吗？");
        this.form.exceldocContent = JSON.stringify(this.hotInstance.getData());
        const date = new Date();
        this.form.exceldocDate = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, "0")}-${String(date.getDate()).padStart(2, "0")}`;
        this.form.userId = this.$store.state.user.id;

        if (this.form.userexcelId == null) {
          await addExcelData(this.form);
        } else {
          await updateExcelData(this.form);
        }
        this.$modal.msgSuccess("保存成功");
      } catch (error) {
        if (error !== "cancel") {
          this.$message.error("保存失败: " + (error.message || error));
        }
      }
    },

    async exportToExcel() {
      if (!this.hotInstance) {
        this.$modal.msgError("没有可导出的数据！");
        return;
      }

      const workbook = new ExcelJS.Workbook();
      const worksheet = workbook.addWorksheet("Sheet1");

      const data = this.hotInstance.getData();
      data.forEach((row, rowIndex) => {
        const excelRow = worksheet.addRow(row);
        row.forEach((_, colIndex) => {
          const address = this.encodeCellAddress(rowIndex, colIndex);
          const cell = worksheet.getCell(address);
          const style = this.cellStyles[address];

          cell.border = {
            top: { style: "thin", color: { argb: "FF000000" } },
            left: { style: "thin", color: { argb: "FF000000" } },
            bottom: { style: "thin", color: { argb: "FF000000" } },
            right: { style: "thin", color: { argb: "FF000000" } }
          };

          if (style) {
            if (style.backgroundColor) {
              cell.fill = {
                type: "pattern",
                pattern: "solid",
                fgColor: { argb: style.backgroundColor.replace("#", "") }
              };
            }
            if (style.fontColor || style.bold || style.italic || style.fontSize) {
              cell.font = {
                color: { argb: style.fontColor?.replace("#", "") || "FF000000" },
                bold: style.bold || false,
                italic: style.italic || false,
                size: style.fontSize || 12
              };
            }
            if (style.textAlign) {
              cell.alignment = { horizontal: style.textAlign };
            }
          }
        });
      });

      this.mergeCells.forEach(({ row, col, rowspan, colspan }) => {
        const start = this.encodeCellAddress(row, col);
        const end = this.encodeCellAddress(row + rowspan - 1, col + colspan - 1);
        worksheet.mergeCells(`${start}:${end}`);
      });

      const buffer = await workbook.xlsx.writeBuffer();
      const blob = new Blob([buffer], { type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" });
      const link = document.createElement("a");
      link.href = window.URL.createObjectURL(blob);
      link.download = "导出数据.xlsx";
      link.click();

      this.$modal.msgSuccess("Excel 导出成功！");
    },

    async loadExcelData(arrayBuffer, savedData) {
      const workbook = new ExcelJS.Workbook();
      await workbook.xlsx.load(arrayBuffer);
      const worksheet = workbook.getWorksheet(1);

      if (savedData) {
        this.mergeDataToWorksheet(worksheet, savedData);
      }

      const data = this.extractSheetData(worksheet);
      this.cellStyles = this.extractCellStyles(worksheet);
      this.mergeCells = this.extractMergeCells(worksheet);

      this.initHyperFormula();
      this.renderHandsontable(data);

      this.$nextTick(() => {
        this.replaceErrorsWithDefault();
      });
    },

    mergeDataToWorksheet(worksheet, savedData) {
      savedData?.forEach((row, rowIndex) => {
        row.forEach((cellValue, colIndex) => {
          worksheet.getCell(rowIndex + 1, colIndex + 1).value = cellValue;
        });
      });
    },

    replaceErrorsWithDefault() {
      if (!this.hotInstance) return;
      const rowCount = this.hotInstance.countRows();
      const colCount = this.hotInstance.countCols();
      for (let row = 0; row < rowCount; row++) {
        for (let col = 0; col < colCount; col++) {
          const value = this.hotInstance.getDataAtCell(row, col);
          if (value === "#ERROR!") {
            this.hotInstance.setDataAtCell(row, col, "0.00");
          }
        }
      }
    },

    extractSheetData(worksheet) {
      const data = [];
      worksheet.eachRow({ includeEmpty: true }, row => {
        const rowData = [];
        row.eachCell({ includeEmpty: true }, cell => {
          let value = cell.value;
          if (cell.formula) value = `=${cell.formula}`;
          if (value && typeof value === "object") {
            if (value.richText) value = value.richText.map(rt => rt.text).join("");
            if (value.result !== undefined) value = value.result;
          }
          rowData.push(value || "");
        });
        data.push(rowData);
      });
      return data;
    },

    extractMergeCells(worksheet) {
      const mergeCells = [];
      worksheet.model.merges.forEach(merge => {
        const [start, end] = merge.split(":");
        const startCell = this.decodeCellAddress(start);
        const endCell = this.decodeCellAddress(end);
        mergeCells.push({
          row: startCell.row,
          col: startCell.col,
          rowspan: endCell.row - startCell.row + 1,
          colspan: endCell.col - startCell.col + 1
        });
      });
      return mergeCells;
    },

    extractCellStyles(worksheet) {
      const styles = {};
      worksheet.eachRow({ includeEmpty: true }, (row, rowIndex) => {
        row.eachCell({ includeEmpty: true }, (cell, colIndex) => {
          const address = this.encodeCellAddress(rowIndex - 1, colIndex - 1);
          const style = cell.style;
          styles[address] = {
            backgroundColor: style.fill?.fgColor?.argb ? `#${style.fill.fgColor.argb.slice(2)}` : null,
            fontColor: style.font?.color?.argb ? `#${style.font.color.argb.slice(2)}` : null,
            bold: style.font?.bold || false,
            italic: style.font?.italic || false,
            fontSize: style.font?.size || 12,
            textAlign: style.alignment?.horizontal || "left"
          };
        });
      });
      return styles;
    },

    decodeCellAddress(address) {
      const match = address.match(/([A-Z]+)(\d+)/);
      const colLetters = match[1];
      const rowNumber = parseInt(match[2], 10) - 1;
      let colNumber = 0;
      for (let i = 0; i < colLetters.length; i++) {
        colNumber = colNumber * 26 + (colLetters.charCodeAt(i) - 64);
      }
      return { row: rowNumber, col: colNumber - 1 };
    },

    encodeCellAddress(row, col) {
      let colLetters = "";
      col += 1;
      while (col > 0) {
        const remainder = (col - 1) % 26;
        colLetters = String.fromCharCode(65 + remainder) + colLetters;
        col = Math.floor((col - 1) / 26);
      }
      return `${colLetters}${row + 1}`;
    },

    initHyperFormula() {
      this.hyperformula = HyperFormula.buildEmpty({
        licenseKey: "internal-use-in-handsontable"
      });
    },

    renderHandsontable(data) {
      if (this.hotInstance) this.hotInstance.destroy();

      this.hotInstance = new Handsontable(this.$refs.hotTable, {
        data,
        rowHeaders: true,
        colHeaders: true,
        mergeCells: this.mergeCells,
        formulas: { engine: this.hyperformula },
        licenseKey: "non-commercial-and-evaluation",
        cells: (row, col) => ({ renderer: this.customRenderer })
      });
    },

    customRenderer(instance, td, row, col, prop, value, cellProperties) {
      Handsontable.renderers.TextRenderer.apply(this, arguments);
      const address = this.encodeCellAddress(row, col);
      const style = this.cellStyles[address];
      if (style) {
        td.style.backgroundColor = style.backgroundColor || "";
        td.style.color = style.fontColor || "";
        td.style.fontWeight = style.bold ? "bold" : "";
        td.style.fontStyle = style.italic ? "italic" : "";
        td.style.textAlign = style.textAlign || "";
      }
    }
  }
};
</script>

<style scoped>
.chat-app {
  display: flex;
  height: 100vh;
}
.left-side {
  width: 150px;
  border-right: 1px solid #ddd;
  padding: 10px;
  overflow-y: auto;
}
.right-side {
  flex: 1;
  display: flex;
  flex-direction: column;
}
.toolbar {
  padding: 10px;
  border-bottom: 1px solid #eee;
}
.handsontable-container {
  flex: 1;
  overflow: auto;
}
</style>
