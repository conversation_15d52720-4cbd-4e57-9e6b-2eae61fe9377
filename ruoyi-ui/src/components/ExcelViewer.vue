<template>
  <div>
    <input type="file" @change="handleFileUpload" accept=".xlsx, .xls" />
    <div ref="hotTable"></div>
  </div>
</template>

<script>
import Handsontable from "handsontable";
import "handsontable/dist/handsontable.full.css";
import * as XLSX from "xlsx";

export default {
  data() {
    return {
      hotInstance: null,
    };
  },
  methods: {
    handleFileUpload(event) {
      const file = event.target.files[0];
      if (!file) return;

      const reader = new FileReader();
      reader.readAsBinaryString(file);
      reader.onload = (e) => {
        const binaryData = e.target.result;
        const workbook = XLSX.read(binaryData, { type: "binary" });

        // 读取第一个工作表
        const sheetName = workbook.SheetNames[0];
        const sheet = workbook.Sheets[sheetName];

        // 将数据转换为二维数组
        const data = XLSX.utils.sheet_to_json(sheet, { header: 1 });

        // 更新 Handsontable 数据
        if (this.hotInstance) {
          this.hotInstance.loadData(data);
        }
      };
    },
  },
  mounted() {
    this.hotInstance = new Handsontable(this.$refs.hotTable, {
      data: [],
      rowHeaders: true,
      colHeaders: true,
      licenseKey: "non-commercial-and-evaluation",
    });
  },
};
</script>

<style>
/* 可选：定义 Handsontable 的容器样式 */
</style>
