<template>
  <div :id="divId" :class="['message-container', sender]">
    <div class="message-avatar">
      <div v-if="sender === 'user'" class="avatar user-avatar">
        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2" />
          <circle cx="12" cy="7" r="4" />
        </svg>
      </div>
      <div v-else class="avatar ai-avatar">
        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <path d="M9 12l2 2 4-4" />
          <path d="M21 12c.552 0 1-.448 1-1s-.448-1-1-1-1 .448-1 1 .448 1 1 1z" />
          <path d="M3 12c.552 0 1-.448 1-1s-.448-1-1-1-1 .448-1 1 .448 1 1 1z" />
        </svg>
      </div>
    </div>

    <div class="message-content">
      <div class="message-bubble">
        <div class="message-text" v-html="formatMessage(message.text)" />
      </div>
      <div v-if="message.timestamp" class="message-time">
        {{ formatTime(message.timestamp) }}
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    message: {
      type: Object,
      required: true
    }
  },
  computed: {
    sender() {
      return this.message.sender
    },
    senderName() {
      return this.message.sender === 'user' ? '您' : '周会计'
    },
    divId() {
      return 'message' + (this.message.index || Math.random().toString(36).substr(2, 9))
    }
  },
  methods: {
    formatTime(timestamp) {
      if (!timestamp) return ''
      const date = new Date(timestamp)
      const now = new Date()
      const diffInMinutes = Math.floor((now - date) / (1000 * 60))

      if (diffInMinutes < 1) return '刚刚'
      if (diffInMinutes < 60) return diffInMinutes + '分钟前'
      if (diffInMinutes < 1440) return Math.floor(diffInMinutes / 60) + '小时前'

      return date.toLocaleDateString('zh-CN', {
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      })
    },
    formatMessage(text) {
      if (!text) return ''

      // 简单的 Markdown 支持
      return text
        .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
        .replace(/\*(.*?)\*/g, '<em>$1</em>')
        .replace(/`(.*?)`/g, '<code>$1</code>')
        .replace(/\n/g, '<br>')
    }
  }
}
</script>

<style scoped>
.message-container {
  display: flex;
  margin-bottom: 20px;
  align-items: flex-start;
  max-width: 100%;
  position: relative;
}

.message-container.user {
  justify-content: flex-end;
}

.message-avatar {
  position: absolute;
  top: 0;
}

.message-container:not(.user) .message-avatar {
  left: -52px; /* 精确对齐：20px padding + 32px 头像宽度，头像左边缘与容器边缘对齐 */
}

.message-container.user .message-avatar {
  right: -52px; /* 精确对齐：20px padding + 32px 头像宽度，头像右边缘与容器边缘对齐 */
}

.avatar {
  width: 32px;
  height: 32px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #ffffff;
}

.user-avatar {
  background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
}

.ai-avatar {
  background: linear-gradient(135deg, #f0f0f0 0%, #d9d9d9 100%);
  color: #333333;
}

.avatar svg {
  width: 18px;
  height: 18px;
}

.message-content {
  min-width: 0;
  margin-left: 0; /* AI头像现在在外部，不需要内部留空间 */
  margin-right: 0;
  max-width: 100%;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.message-container.user .message-content {
  margin-left: 0;
  margin-right: 0; /* 用户头像现在在padding区域 */
  align-items: flex-end; /* 用户消息右对齐 */
}

.message-time {
  font-size: 11px;
  color: #999999;
  margin-top: 6px;
  opacity: 0;
  transition: opacity 0.2s ease;
  align-self: flex-start;
}

.message-container.user .message-time {
  align-self: flex-end;
}

.message-container:hover .message-time {
  opacity: 1;
}

.message-bubble {
  background: #f5f5f5;
  border: 1px solid #e8e8e8;
  border-radius: 16px;
  padding: 16px 20px; /* 增加内边距，使文字更居中 */
  position: relative;
  max-width: 600px; /* 固定最大宽度，类似ChatGPT */
  min-width: 80px; /* 设置最小宽度，防止气泡被压缩成竖直状态 */
  word-wrap: break-word;
  hyphens: auto;
  display: inline-block; /* 确保气泡正常显示 */
  width: fit-content;
  text-align: left; /* 确保文字左对齐，便于阅读 */
}

.user .message-bubble {
  background: #1890ff;
  border-color: #1890ff;
}

.message-text {
  font-size: 15px;
  line-height: 1.6; /* 增加行间距，提高可读性 */
  color: #333333;
  word-wrap: break-word;
  white-space: pre-wrap;
  overflow-wrap: break-word;
  min-width: 0; /* 防止文本过度收缩 */
  margin: 0; /* 确保没有额外的边距 */
  text-align: left; /* 文字左对齐 */
}

.user .message-text {
  color: #ffffff;
}

.message-text strong {
  font-weight: 700;
}

.message-text em {
  font-style: italic;
}

.message-text code {
  background: rgba(0, 0, 0, 0.04);
  border: 1px solid rgba(0, 0, 0, 0.06);
  border-radius: 4px;
  padding: 2px 6px;
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  font-size: 0.9em;
  color: #333333;
}

.user .message-text code {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.3);
  color: #ffffff;
}

/* 移除消息气泡箭头，采用更简洁的ChatGPT风格 */

/* 动画效果 */
.message-container {
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .message-container {
    margin-bottom: 16px;
  }

  .message-container:not(.user) .message-avatar {
    left: -44px; /* 移动端AI头像精确对齐：16px padding + 28px 头像宽度 = 44px */
  }

  .message-container.user .message-avatar {
    right: -44px; /* 移动端用户头像精确对齐：16px padding + 28px 头像宽度 = 44px */
  }

  .message-content {
    margin-left: 0; /* 移动端AI头像也在外部 */
  }

  .message-container.user .message-content {
    margin-left: 0;
    margin-right: 0; /* 移动端用户头像也在padding区域 */
  }

  .avatar {
    width: 28px;
    height: 28px;
  }

  .avatar svg {
    width: 16px;
    height: 16px;
  }

  .message-bubble {
    padding: 14px 16px; /* 移动端也增加内边距 */
    max-width: 480px; /* 移动端固定最大宽度 */
    border-radius: 14px;
  }

  .message-text {
    font-size: 14px;
    line-height: 1.4;
  }
}
</style>
