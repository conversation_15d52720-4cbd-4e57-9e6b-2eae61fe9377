<template>
  <!-- 根容器现在是布局的核心，它会根据 sender 决定自己的对齐方式和内容顺序 -->
  <div :class="['message-row', message.sender]">
    <!-- 头像 -->
    <div class="avatar">
      <div v-if="message.sender === 'user'" class="user-avatar avatar-container">
        <div class="avatar-inner">
          <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round">
            <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"/>
            <circle cx="12" cy="7" r="4"/>
          </svg>
        </div>
      </div>
      <div v-else class="ai-avatar avatar-container">
        <div class="avatar-inner">
          <div class="cute-face">
            <div class="eyes">
              <div class="eye left"></div>
              <div class="eye right"></div>
            </div>
            <div class="mouth"></div>
          </div>
        </div>
      </div>
    </div>

    <!-- 消息气泡 -->
    <div class="message-bubble">
      <div class="message-text" v-html="formatMessage(message.text)" />
    </div>
  </div>
</template>

<script>
export default {
  props: {
    message: {
      type: Object,
      required: true
    }
  },
  methods: {
    formatMessage(text) {
      if (!text) return ''
      return text
        .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
        .replace(/\*(.*?)\*/g, '<em>$1</em>')
        .replace(/`(.*?)`/g, '<code>$1</code>')
        .replace(/\n/g, '<br>')
    }
  }
}
</script>

<style scoped>
/* 整个消息行：一个灵活的容器 */
.message-row {
  display: flex;
  align-items: flex-start; /* 让头像和气泡顶部对齐，长消息时头像不会跑到底部 */
  gap: 12px; /* 头像和气泡之间的固定间距 */
  max-width: 80%; /* 限制消息行的最大宽度，避免单条消息过长 */
}

/* AI 消息（默认，靠左）*/
.message-row.ai {
  align-self: flex-start; /* 关键：让整个消息行在父容器中靠左 */
}

/* 用户消息（靠右，且内容反向）*/
.message-row.user {
  align-self: flex-end; /* 关键：让整个消息行在父容器中靠右 */
  flex-direction: row-reverse; /* 关键：让头像和气泡的位置颠倒，头像就在右边了 */
}

/* --- 头像和气泡的样式 --- */

.avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  flex-shrink: 0;
  background-color: #d1d5db;
}

.avatar-container {
  position: relative;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  flex-shrink: 0;
  transition: all 0.3s ease;
  cursor: pointer;
}

.avatar-container:hover {
  transform: scale(1.1);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
}

.avatar-inner {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

.user-avatar {
  background: linear-gradient(135deg, #4A90E2 0%, #357ABD 100%);
  box-shadow: 0 2px 8px rgba(74, 144, 226, 0.2);
  position: relative;
}

.user-avatar svg {
  width: 20px;
  height: 20px;
  color: white;
  stroke-width: 1.5;
}

.ai-avatar {
  background: linear-gradient(135deg, #f0f0f0 0%, #e0e0e0 100%);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  position: relative;
}

.ai-avatar::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(135deg, #f0f0f0, #e0e0e0, #f5f5f5, #e8e8e8);
  border-radius: 50%;
  z-index: -1;
  animation: borderRotate 3s linear infinite;
}

.cute-face {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
}

.eyes {
  display: flex;
  gap: 4px;
  margin-bottom: 2px;
}

.eye {
  width: 3px;
  height: 3px;
  background: #333;
  border-radius: 50%;
  animation: blink 3s infinite;
}

.mouth {
  width: 8px;
  height: 4px;
  border: 1px solid #333;
  border-top: none;
  border-radius: 0 0 8px 8px;
  margin-top: 1px;
}

@keyframes blink {
  0%, 95% {
    transform: scaleY(1);
  }
  97.5% {
    transform: scaleY(0.1);
  }
}

.avatar-container svg {
  width: 22px;
  height: 22px;
  color: white;
  z-index: 1;
}

.user-avatar svg {
  color: white;
}

@keyframes borderRotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 0.8;
    transform: translate(-50%, -50%) scale(1);
  }
  50% {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1.2);
  }
}

.message-bubble {
  padding: 12px 16px;
  border-radius: 18px;
  line-height: 1.6;
  font-size: 15px;
  word-break: break-word;
}

.message-row.ai .message-bubble {
  background-color: #f0f2f5;
  color: #333;
  border-top-left-radius: 6px; /* 营造一个小三角的视觉效果 */
}

.message-row.user .message-bubble {
  background-color: #1890ff;
  color: white;
  border-top-right-radius: 6px; /* 营造一个小三角的视觉效果 */
}

.message-text {
  white-space: pre-wrap;
}

.message-text strong {
  font-weight: 600;
}

.message-text em {
  font-style: italic;
}

.message-text code {
  background-color: rgba(0, 0, 0, 0.1);
  padding: 2px 4px;
  border-radius: 3px;
  font-family: 'Courier New', monospace;
  font-size: 0.9em;
}

/* 简单的进入动画 */
.message-row {
  animation: slideIn 0.3s ease-out forwards;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>