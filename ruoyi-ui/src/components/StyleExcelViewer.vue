<template>
  <div id="app">
    <h1>Univer.js v0.6.4 - Excel 导入示例</h1>

    <!-- 文件上传按钮 -->
    <input type="file" @change="handleFileUpload" accept=".xlsx, .xls" />

    <!-- 表格容器 -->
    <div ref="univerContainer" style="width: 100%; height: 600px; border: 1px solid #ccc;"></div>
  </div>
</template>

<script>
import { Univer } from '@univerjs/core';                      // 核心模块
import { RenderEngine } from '@univerjs/engine-render';       // 渲染引擎
import { UniverSheet, Workbook } from '@univerjs/sheets';     // 表格插件
import * as XLSX from 'xlsx';                                 // Excel解析库
import XLSX_CALC from 'xlsx-calc';                            // Excel公式计算库

export default {
  name: 'App',
  data() {
    return {
      univer: null,       // Univer 实例
      workbook: null      // Workbook 实例
    };
  },
  mounted() {
    this.initUniver();
  },
  methods: {
    // ✅ 初始化 Univer.js 实例
    initUniver() {
      // 1. 创建 Univer 实例并挂载到容器
      this.univer = new Univer({
        container: this.$refs.univerContainer
      });

      // 2. 注册插件
      const renderEngine = new RenderEngine();
      const sheetPlugin = new UniverSheet();

      this.univer.registerPlugin(renderEngine);
      this.univer.registerPlugin(sheetPlugin);

      // 3. 创建空 Workbook 并加载
      this.workbook = new Workbook();
      this.univer.loadWorkbook(this.workbook);
    },

    // ✅ 处理文件上传
    handleFileUpload(event) {
      const file = event.target.files[0];
      if (!file) return;

      const reader = new FileReader();
      reader.onload = async (e) => {
        const binaryStr = e.target.result;

        // 解析 Excel
        const workbook = XLSX.read(binaryStr, { type: 'binary', cellStyles: true });

        // 计算 Excel 公式
        XLSX_CALC(workbook);

        // 将 Excel 转为 Univer.js 格式
        const sheets = this.parseExcel(workbook);

        // 将数据加载到表格中
        this.loadExcelData(sheets);
      };
      reader.readAsBinaryString(file);
    },

    // ✅ 将 Excel 数据加载到 Univer.js
    loadExcelData(sheets) {
      const newWorkbook = new Workbook();

      sheets.forEach((sheet) => {
        const univerSheet = newWorkbook.createSheet(sheet.name);

        // 填充数据和样式
        Object.keys(sheet.cellData).forEach((cellRef) => {
          const { v, f, style } = sheet.cellData[cellRef];
          const { c, r } = XLSX.utils.decode_cell(cellRef);

          // 设置单元格数据
          if (f) {
            univerSheet.setCellFormula(r, c, f);
          } else {
            univerSheet.setCellValue(r, c, v);
          }

          // 应用样式
          if (style) {
            univerSheet.setStyle(r, c, {
              background: style.backgroundColor || 'transparent',
              color: style.color || '#000000',
              bold: style.fontWeight === 'bold',
              italic: style.fontStyle === 'italic',
              fontSize: parseInt(style.fontSize) || 12
            });
          }
        });
      });

      this.univer.loadWorkbook(newWorkbook);
    },

    // ✅ 解析 Excel 数据和样式
    parseExcel(workbook) {
      const sheets = [];

      workbook.SheetNames.forEach((sheetName) => {
        const sheet = workbook.Sheets[sheetName];
        const cellData = {};
        const range = XLSX.utils.decode_range(sheet['!ref']);

        for (let R = range.s.r; R <= range.e.r; ++R) {
          for (let C = range.s.c; C <= range.e.c; ++C) {
            const cellRef = XLSX.utils.encode_cell({ r: R, c: C });
            const cell = sheet[cellRef];

            if (cell) {
              const style = this.getCellStyle(cell);
              cellData[cellRef] = {
                v: cell.v || '',
                f: cell.f || '',
                style
              };
            }
          }
        }

        sheets.push({
          name: sheetName,
          cellData
        });
      });

      return sheets;
    },

    // ✅ 提取单元格样式，防止解析失败
    getCellStyle(cell) {
      if (!cell || !cell.s) return {};

      const style = {};

      if (cell.s.fill) {
        style.backgroundColor = cell.s.fill.fgColor ? `#${cell.s.fill.fgColor.rgb}` : '#FFFFFF';
      }

      if (cell.s.font) {
        style.color = cell.s.font.color ? `#${cell.s.font.color.rgb}` : '#000000';
        style.fontWeight = cell.s.font.bold ? 'bold' : 'normal';
        style.fontStyle = cell.s.font.italic ? 'italic' : 'normal';
        style.fontSize = `${cell.s.font.sz || 12}px`;
      }

      return style;
    }
  }
};
</script>

<style scoped>
h1 {
  text-align: center;
  font-size: 24px;
}
</style>
