<template>
  <div ref="sheetContainer" class="sheet-container"></div>
</template>

<script>
import { Univer, UniverInstanceType } from '@univerjs/core';
import { RenderEngine } from '@univerjs/engine-render';
import { SheetContext, Worksheet } from '@univerjs/core';

export default {
  name: 'UniverSheet',
  mounted() {
    this.initUniver();
  },
  methods: {
    initUniver() {
      // 初始化 Univer 实例
      const univer = new Univer();

      // 创建一个新的工作簿
      const workbook = univer.createUniverSheet(UniverInstanceType.SHEET);

      // 获取第一个工作表
      const sheet = workbook.getActiveSheet();

      // 设置工作表名称
      sheet.setName('My Sheet');

      // 设置单元格数据
      sheet.getCell(0, 0).setValue('Hello');
      sheet.getCell(0, 1).setValue('World');
      sheet.getCell(1, 0).setValue(123);
      sheet.getCell(1, 1).setValue(456);

      // 初始化渲染引擎
      const renderEngine = new RenderEngine();

      // 创建 SheetContext
      const sheetContext = new SheetContext(workbook, renderEngine);

      // 渲染工作表
      const worksheet = new Worksheet(sheetContext, sheet);

      // 将工作表渲染到 DOM 中
      worksheet.render(this.$refs.sheetContainer);
    },
  },
};
</script>

<style scoped>
.sheet-container {
  width: 100%;
  height: 500px;
  border: 1px solid #ccc;
}
</style>
