<template>
  <div class="collaborative-learning-space">
    <!-- 头部 -->
    <header class="space-header">
      <h1>协作学习空间</h1>
      <p>欢迎来到协作学习空间，与您的同伴一起学习和分享！</p>
    </header>

    <!-- 主体内容 -->
    <main class="space-main">
      <!-- 左侧：聊天和文件共享 -->
      <div class="left-panel">
        <div class="chat-section">
          <h2>聊天室</h2>
          <div class="chat-messages">
            <div v-for="(message, index) in messages" :key="index" class="message">
              <strong>{{ message.user }}:</strong> {{ message.text }}
            </div>
          </div>
          <input v-model="newMessage" @keyup.enter="sendMessage" placeholder="输入消息并按下回车发送" />
        </div>

        <div class="file-sharing">
          <h2>文件共享</h2>
          <input type="file" @change="handleFileUpload" />
          <ul>
            <li v-for="(file, index) in sharedFiles" :key="index">{{ file.name }}</li>
          </ul>
        </div>
      </div>

      <!-- 右侧：白板协作 -->
      <div class="right-panel">
        <h2>协作白板</h2>
        <canvas ref="whiteboard" @mousedown="startDrawing" @mousemove="draw" @mouseup="stopDrawing"></canvas>
      </div>
    </main>
  </div>
</template>

<script>

export default {
  name: 'CollaborativeLearningSpace',
  data() {
    return {
      messages: [], // 聊天消息
      newMessage: '', // 新消息输入
      sharedFiles: [], // 共享文件列表
      isDrawing: false, // 是否正在绘制
      lastX: 0,
      lastY: 0,

      //联系人列表
            contactList: [],
            contactListTotal: 0,
            contactListLoading: false,
            //消息记录
            msgList: [],
            msgListTotal: 0,
            msgListLoading: false,
            inputVal: '',
            search: '',
            contactUserId: null,
            userId: null,
            contactQueryParams: {
              pageSize: 10,
              pageNum: 1
            },
            currentContact: {}
    };
  },
  methods: {
    // 发送消息
    sendMessage() {
      if (this.newMessage.trim()) {
        this.messages.push({ user: '你', text: this.newMessage });
        //this.newMessage = '';
        console.log(1);
        const message = {
                contactId: this.currentContact.id,
                userId: this.userId,
                content: this.inputVal,
                roomId: this.currentContact.roomId
              }
              console.log(2);
              this.msgList.push({
                ...message,
                id: this.msgList.length + 1,
                createTime: ''
              });
              console.log(3);
              console.log(4);
              //addMessage(message);
              const msg = {
                  sendUserId: this.userId,
                  sendUserName: this.$store.state.user.name,
                  userId: this.currentContact.contactUserId,
                  type: "chat",
                  detail: this.newMessage
              }
              console.log(5);
              console.log(this.newMessage);
              this.socket = new WebSocket('ws://localhost:8080/openai/1');
              console.log(this.socket);
              this.socket.send(JSON.stringify(msg));
              console.log(6);
              this.inputVal = '';
              console.log(7);
              this.fleshScroll();
              console.log(8);
      }
    },


    // 处理文件上传
    handleFileUpload(event) {
      const file = event.target.files[0];
      if (file) {
        this.sharedFiles.push(file);
      }
    },
    // 开始绘制
    startDrawing(event) {
      this.isDrawing = true;
      const canvas = this.$refs.whiteboard;
      const rect = canvas.getBoundingClientRect();
      this.lastX = event.clientX - rect.left;
      this.lastY = event.clientY - rect.top;
    },
    // 绘制
    draw(event) {
      if (!this.isDrawing) return;
      const canvas = this.$refs.whiteboard;
      const ctx = canvas.getContext('2d');
      const rect = canvas.getBoundingClientRect();
      const x = event.clientX - rect.left;
      const y = event.clientY - rect.top;

      ctx.beginPath();
      ctx.moveTo(this.lastX, this.lastY);
      ctx.lineTo(x, y);
      ctx.strokeStyle = '#000';
      ctx.lineWidth = 2;
      ctx.stroke();

      this.lastX = x;
      this.lastY = y;
    },
    // 停止绘制
    stopDrawing() {
      this.isDrawing = false;
    },
  },
  mounted() {
    // 初始化白板
    const canvas = this.$refs.whiteboard;
    canvas.width = canvas.offsetWidth;
    canvas.height = canvas.offsetHeight;
  },
};
</script>

<style scoped>
.collaborative-learning-space {
  display: flex;
  flex-direction: column;
  height: 100vh;
  padding: 1rem;
}

.space-header {
  text-align: center;
  margin-bottom: 2rem;
}

.space-main {
  display: flex;
  flex: 1;
  gap: 1rem;
}

.left-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.right-panel {
  flex: 2;
}

.chat-section {
  border: 1px solid #ccc;
  padding: 1rem;
  border-radius: 8px;
}

.chat-messages {
  height: 200px;
  overflow-y: auto;
  margin-bottom: 1rem;
}

.message {
  margin-bottom: 0.5rem;
}

.file-sharing {
  border: 1px solid #ccc;
  padding: 1rem;
  border-radius: 8px;
}

canvas {
  border: 1px solid #ccc;
  width: 100%;
  height: 500px;
  background-color: #f9f9f9;
}
</style>
