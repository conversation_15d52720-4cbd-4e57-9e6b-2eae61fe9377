<template>
  <div class="chat-app">
    <!-- 左侧边栏 -->
    <div class="sidebar">
      <!-- 新建对话按钮 -->
      <div class="new-chat-container">
        <button class="new-chat-btn" @click="newMessage">
          <svg class="new-chat-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <line x1="12" y1="5" x2="12" y2="19" />
            <line x1="5" y1="12" x2="19" y2="12" />
          </svg>
          新建对话
        </button>
      </div>

      <!-- 聊天历史 -->
      <div class="chat-history">
        <div class="history-section">
          <div class="history-title">最近对话</div>
          <div v-if="chatHistory.length === 0" class="no-history">
            <div class="no-history-icon">
              <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="m3 21 1.9-5.7a8.5 8.5 0 1 1 3.8 3.8z" />
              </svg>
            </div>
            <span class="no-history-text">暂无对话历史</span>
          </div>
          <div v-for="(item, index) in chatHistory" :key="`chat-${index}-${item.title}`" class="history-item"
            :class="{ active: selectedChat === index }" @click="selectChat(index, item)">
            <div class="history-content">
              <svg class="history-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="m3 21 1.9-5.7a8.5 8.5 0 1 1 3.8 3.8z" />
              </svg>
              <span class="history-text" :title="item.title">{{ item.title || '新对话' }}</span>
            </div>
            <div class="history-actions">
              <button class="action-btn" :title="'删除对话'" @click.stop="deleteChat(index)">
                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <polyline points="3,6 5,6 21,6" />
                  <path d="m19,6v14a2,2 0,0 1,-2,2H7a2,2 0,0 1,-2,-2V6m3,0V4a2,2 0,0 1,2,-2h4a2,2 0,0 1,2,2v2" />
                </svg>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 主聊天区域 -->
    <div class="main-content">
      <!-- 聊天标题栏 -->
      <div class="chat-header">
        <div class="header-content">
          <div class="chat-title">
            <svg class="title-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M9 12l2 2 4-4" />
              <path d="M21 12c.552 0 1-.448 1-1s-.448-1-1-1-1 .448-1 1 .448 1 1 1z" />
              <path d="M3 12c.552 0 1-.448 1-1s-.448-1-1-1-1 .448-1 1 .448 1 1 1z" />
            </svg>
            <h1>{{ chatLabel || 'AI智能助手' }}</h1>
          </div>
          <div class="status-indicator">
            <div class="status-dot online" />
            <span class="status-text">在线</span>
          </div>
        </div>
      </div>

      <!-- 消息区域 -->
      <div class="messages-container">
        <div class="messages-wrapper">
          <!-- 欢迎消息 -->
          <div v-if="messages.length === 0 && !selectedChat" class="welcome-message">
            <div class="welcome-content">
              <div class="welcome-icon">
                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <path d="M9 12l2 2 4-4" />
                  <path d="M21 12c.552 0 1-.448 1-1s-.448-1-1-1-1 .448-1 1 .448 1 1 1z" />
                  <path d="M3 12c.552 0 1-.448 1-1s-.448-1-1-1-1 .448-1 1 .448 1 1 1z" />
                </svg>
              </div>
              <h3 class="welcome-title">欢迎使用AI智能助手</h3>
              <p class="welcome-desc">您可以向我提问任何问题，我会尽力为您解答。</p>
            </div>
          </div>

          <MessageBubble v-for="(msg, i) in messages" :key="i" :message="msg" />
          <!-- <MessageBubble :message="'aaa'" />
          <MessageBubble :message="'aaa'" />
          <MessageBubble :message="'aaa'" />
          <MessageBubble :message="'aaa'" />
          <MessageBubble :message="'aaa'" />
          <MessageBubble :message="'aaa'" />
          <MessageBubble :message="'aaa'" />
          <MessageBubble :message="'aaa'" />
          <MessageBubble :message="'aaa'" />
          <MessageBubble :message="'aaa'" />
          <MessageBubble :message="'aaa'" />
          <MessageBubble :message="'aaa'" />
          <MessageBubble :message="'aaa'" />
          <MessageBubble :message="'aaa'" /> -->
          <div v-if="isTyping" class="typing-indicator">
            <div class="typing-bubble">
              <div class="typing-dots">
                <span />
                <span />
                <span />
              </div>
            </div>
          </div>
        </div>
        <!-- 输入区域 - ChatGPT风格 -->
        <div class="input-container">
          <div class="input-wrapper">
            <div class="input-box">
              <!-- 输入框 -->
              <div class="input-area">
                <textarea ref="messageInput" v-model="userInput" placeholder="请输入您的问题..." class="message-input" rows="1"
                  @keydown="handleKeyDown" @input="autoResize" />
              </div>

              <!-- 右侧功能按钮 -->
              <div class="right-actions">
                <button class="send-button" :disabled="!userInput.trim() || isTyping" @click="sendMessage" title="发送消息">
                  <svg class="send-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <line x1="22" y1="2" x2="11" y2="13" />
                    <polygon points="22,2 15,22 11,13 2,9 22,2" />
                  </svg>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>


    </div>
  </div>
</template>

<script>
import MessageBubble from '@/components/MessageBubble'
import { parseTime } from '@/utils/ruoyi'
import { getTree, addChathistory } from '@/api/system/chathistory'
import { getDicts } from '@/api/system/dict/data'
import store from '@/store'
// import { completion } from "@/api/system/introductionjob";

export default {
  components: { MessageBubble },
  data() {
    return {
      userId: null,
      treeData: [],
      defaultProps: { children: 'children', label: 'label' },
      messages: [],
      userInput: '',
      chatLabel: '',
      chatDict: [],
      chatHistory: [],
      selectedChat: null,
      isTyping: false,
      debugMode: process.env.NODE_ENV === 'development', // 开发环境下启用调试
      form: {
        chatId: null,
        userId: null,
        chatTile: null,
        chatContent: null,
        chatTime: null,
        chatRemark: null
      }
    }
  },
  created() {
    this.userId = this.$route.params.id
    store.state.user.id = this.userId
    this.form.userId = this.userId
    this.form.chatId = this.userId

    this.loadTree()
    this.loadSysChatDict()

    this.$websocket.initWebSocket()

    window.addEventListener('onmessageWS', this.onMessage)
  },
  beforeDestroy() {
    this.saveHistory()
    window.removeEventListener('onmessageWS', this.onMessage)
  },
  methods: {
    loadTree() {
      getTree(this.userId).then(res => {
        this.treeData = res
        if (this.debugMode) {
          console.log('原始聊天历史数据:', res)
          console.log('数据条数:', res ? res.length : 0)
        }

        // 调整过滤逻辑，更宽松一些，显示所有有内容的历史记录
        const validChats = res.filter(item => {
          if (!item.label) {
            if (this.debugMode) console.log('过滤掉空label的项:', item)
            return false
          }

          try {
            const messages = JSON.parse(item.label)
            // 如果是数组且有内容，就显示
            if (Array.isArray(messages) && messages.length > 0) {
              return true
            }
            // 如果是对象且有text属性，也显示
            if (messages && typeof messages === 'object' && messages.text) {
              return true
            }
            if (this.debugMode) console.log('过滤掉无效JSON的项:', item.label.substring(0, 100))
            return false
          } catch (e) {
            // JSON解析失败，记录错误但仍显示在列表中
            if (this.debugMode) {
              console.log('JSON解析失败，但保留该项:', e.message)
              console.log('原始数据:', item.label.substring(0, 100))
            }
            const hasContent = item.label.trim().length > 0
            return hasContent
          }
        })

        if (this.debugMode) {
          console.log('过滤后的有效聊天记录:', validChats.length)
        }

        this.chatHistory = validChats.map((item, index) => ({
          id: index,
          title: this.extractTitle(item.label),
          data: item
        }))
      }).catch(error => {
        console.error('加载聊天历史失败:', error)
        this.chatHistory = []
      })
    },
    extractTitle(label) {
      if (!label) return '新对话'

      try {
        const messages = JSON.parse(label)

        // 处理数组格式的消息
        if (Array.isArray(messages) && messages.length > 0) {
          // 查找第一个用户消息作为标题
          const userMessage = messages.find(msg => msg.sender === 'user')
          if (userMessage && userMessage.text) {
            const text = userMessage.text.trim()
            if (text.length > 0) {
              return text.length > 25 ? text.substring(0, 25) + '...' : text
            }
          }
          // 如果没有用户消息，使用第一个有内容的消息
          const firstValidMsg = messages.find(msg => msg.text && msg.text.trim())
          if (firstValidMsg) {
            const text = firstValidMsg.text.trim()
            if (text.length > 0) {
              return text.length > 25 ? text.substring(0, 25) + '...' : text
            }
          }
        }
        // 处理单个对象格式的消息
        else if (messages && typeof messages === 'object' && messages.text) {
          const text = messages.text.trim()
          if (text.length > 0) {
            return text.length > 25 ? text.substring(0, 25) + '...' : text
          }
        }
      } catch (e) {
        // 如果不是JSON格式，直接使用原始内容
        const text = label.trim()
        if (text.length > 0) {
          return text.length > 25 ? text.substring(0, 25) + '...' : text
        }
      }
      return '新对话'
    },
    selectChat(index, item) {
      // 如果点击的是当前选中的对话，不做任何操作
      if (this.selectedChat === index) {
        return
      }

      this.selectedChat = index
      this.handleNodeClick(item.data)

      // 滚动到消息底部
      this.$nextTick(() => {
        this.scrollToBottom()
      })
    },
    deleteChat(index) {
      if (confirm('确认删除这个对话吗？')) {
        this.chatHistory.splice(index, 1)
        if (this.selectedChat === index) {
          this.selectedChat = null
          this.messages = []
        } else if (this.selectedChat > index) {
          this.selectedChat--
        }
      }
    },
    loadSysChatDict() {
      getDicts('sys_chat').then(res => {
        this.chatDict = res.data || []
        this.chatLabel = this.getChatLabelById(this.form.chatId)
      })
    },
    getChatLabelById(id) {
      const item = this.chatDict.find(d => String(d.dictLabel) === String(id))
      return item ? item.dictValue : `用户-${id}`
    },
    handleNodeClick(node) {
      try {
        if (this.debugMode) {
          console.log('点击聊天历史:', node)
          console.log('node.label 内容:', node.label)
        }
        
        if (!node.label) {
          console.error('聊天历史数据为空')
          this.$message.error('聊天历史数据为空')
          return
        }
        
        const messages = JSON.parse(node.label)
        
        if (!Array.isArray(messages)) {
          console.error('聊天历史数据格式错误:', messages)
          this.$message.error('聊天历史数据格式错误')
          return
        }
        
        this.messages = messages
        
        if (this.debugMode) {
          console.log('加载聊天历史成功, 消息数量:', messages.length)
        }
        
      } catch (error) {
        console.error('解析聊天历史失败:', error)
        console.error('原始数据:', node.label)
        this.$message.error('加载聊天历史失败，请稍后重试')
      }
    },

    /** 点击【发送按钮】 */
    sendMessage() {
      if (!this.userInput.trim() || this.isTyping) return

      const messageText = this.userInput.trim()
      this.messages.push({ text: messageText, sender: 'user', timestamp: new Date() })

      const msg = {
        sendUserId: this.userId,
        sendUserName: store.state.user.name || '用户',
        userId: this.userId,
        type: 'chat',
        detail: messageText
      }

      this.$websocket.sendWebsocket(JSON.stringify(msg))

      this.userInput = ''
      this.isTyping = true
      this.autoResize()

      // 自动滚动到底部
      this.$nextTick(() => {
        this.scrollToBottom()
      })
    },

    handleKeyDown(event) {
      if (event.key === 'Enter' && !event.shiftKey) {
        event.preventDefault()
        this.sendMessage()
      }
    },

    autoResize() {
      const textarea = this.$refs.messageInput
      if (textarea) {
        textarea.style.height = 'auto'
        textarea.style.height = Math.min(textarea.scrollHeight, 120) + 'px'
      }
    },

    scrollToBottom() {
      const container = document.querySelector('.messages-wrapper')
      if (container) {
        container.scrollTop = container.scrollHeight
      }
    },

    /** 接收 websocket 消息 */
    onMessage(event) {
      let messageText = ''

      try {
        const data = event.detail.data
        if (this.debugMode) console.log('收到WebSocket消息:', data)

        // 如果是字符串，尝试解析为JSON
        if (typeof data === 'string') {
          // 先检查是否是系统消息或空消息
          if (data === 'true' || data === '请稍后.....') {
            return // 忽略这些系统消息
          }

          // 根据后端代码，AI回复应该直接是文本内容，不是JSON
          // 先尝试直接使用文本内容
          messageText = data

          // 但如果确实是JSON格式，也要处理
          try {
            const parsed = JSON.parse(data)
            if (this.debugMode) console.log('解析后的JSON:', parsed)

            // 检查是否是聊天消息的JSON格式（包含sendUserId等字段）
            if (parsed.sendUserId || parsed.userId) {
              // 这是聊天消息的JSON，提取detail字段
              if (parsed.detail) {
                messageText = parsed.detail
              } else {
                // 如果没有detail字段，忽略这个消息
                if (this.debugMode) console.log('忽略无detail字段的聊天JSON:', data)
                return
              }
            } else {
              // 不是聊天消息的JSON，直接使用原始字符串
              messageText = data
            }
          } catch (e) {
            // 如果不是JSON，直接使用原始字符串（这是期望的情况）
            messageText = data
          }
        } else if (typeof data === 'object' && data !== null) {
          // 如果是对象，提取消息内容
          messageText = data.detail || data.message || data.content || data.text || data.response || JSON.stringify(data)
        } else {
          messageText = String(data)
        }

        if (this.debugMode) console.log('处理后的消息文本:', messageText)

        // 过滤掉一些不需要显示的内容
        if (messageText && messageText.trim() && !this.shouldIgnoreMessage(messageText)) {
          this.isTyping = false
          this.messages.push({
            text: messageText.trim(),
            sender: 'ai',
            timestamp: new Date()
          })

          this.$nextTick(() => {
            this.scrollToBottom()
          })
        } else {
          if (this.debugMode) console.log('消息被过滤或为空:', messageText)
          this.isTyping = false
        }
      } catch (error) {
        console.error('处理websocket消息时出错:', error, event.detail.data)
        this.isTyping = false
      }
    },

    /** 判断是否应该忽略某些消息 */
    shouldIgnoreMessage(message) {
      if (!message || typeof message !== 'string') {
        return true
      }

      const trimmed = message.trim()

      // 过滤掉空消息
      if (trimmed.length === 0) {
        return true
      }

      // 临时完全禁用过滤，让所有AI回复都显示出来，方便调试
      const ignorePatterns = [
        /^true$/,
        /^请稍后\.+$/,
        /^heartbeat$/
      ]

      const shouldIgnore = ignorePatterns.some(pattern => pattern.test(trimmed))
      if (shouldIgnore) {
        if (this.debugMode) console.log('忽略消息:', trimmed)
      }

      return shouldIgnore
    },

    /** 保存当前历史记录 */
    saveHistory() {
      if (!this.messages.length) return

      // 过滤掉空消息和系统消息
      const validMessages = this.messages.filter(msg =>
        msg.text && msg.text.trim() && !this.shouldIgnoreMessage(msg.text)
      )

      if (!validMessages.length) return

      // 截断过长的内容以避免数据库错误
      const chatContent = JSON.stringify(validMessages)
      const truncatedContent = chatContent.length > 2000
        ? chatContent.substring(0, 2000) + '...]'
        : chatContent

      const firstMessageStr = JSON.stringify(validMessages[0])
      const truncatedTitle = firstMessageStr.length > 500
        ? firstMessageStr.substring(0, 500) + '...'
        : firstMessageStr

      this.form.chatTile = truncatedTitle
      this.form.chatContent = truncatedContent
      this.form.chatTime = parseTime(new Date())
      this.form.chatRemark = ''

      addChathistory(this.form).then(() => {
        this.loadTree()
      }).catch(error => {
        console.error('保存聊天历史失败:', error)
        // 即使保存失败，也不影响用户体验
      })
    },

    /** 点击【新建按钮】 */
    newMessage() {
      this.saveHistory()
      this.messages = []
      this.userInput = ''
      this.selectedChat = null
      // 重置输入框高度
      this.$nextTick(() => {
        this.autoResize()
      })
    }
  }
}
</script>

<style scoped>
/* 重置样式 */
.chat-app * {
  box-sizing: border-box;
}

/* 主容器 */
.chat-app {
  display: flex;
  height: 100%;
  background: #ffffff;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
  overflow: hidden !important;
  margin: 0;
  padding: 0;
  position: relative;
}

/* 全局隐藏滚动条 */
html,
body {
  overflow: hidden !important;
  height: 100%;
  margin: 0;
  padding: 0;
}

/* 左侧边栏 */
.sidebar {
  width: 260px;
  background: #fafafa;
  display: flex;
  flex-direction: column;
  border-right: 1px solid #e8e8e8;
  height: 100%;
  overflow: hidden;
}

/* 新建对话按钮容器 */
.new-chat-container {
  padding: 12px;
  border-bottom: 1px solid #e8e8e8;
}

.new-chat-btn {
  width: 100%;
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  background: #ffffff;
  color: #333333;
  border: 1px solid #d9d9d9;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.new-chat-btn:hover {
  background: #f5f5f5;
  border-color: #1890ff;
  color: #1890ff;
}

.new-chat-icon {
  width: 16px;
  height: 16px;
  stroke-width: 2;
}

/* 聊天历史 */
.chat-history {
  flex: 1;
  overflow-y: auto;
  padding: 8px 0;
}

.history-section {
  padding: 0 12px;
}

.history-title {
  color: #666666;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  padding: 8px 4px;
  margin-bottom: 8px;
}

/* 无历史记录状态 */
.no-history {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 32px 12px;
  color: #999999;
  text-align: center;
}

.no-history-icon {
  width: 32px;
  height: 32px;
  margin-bottom: 8px;
  opacity: 0.5;
}

.no-history-icon svg {
  width: 100%;
  height: 100%;
}

.no-history-text {
  font-size: 13px;
  color: #cccccc;
}

.history-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px;
  margin: 2px 0;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  color: #333333;
}

.history-item:hover {
  background: #f0f0f0;
}

.history-item.active {
  background: #e6f7ff;
  color: #1890ff;
}

.history-content {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
  min-width: 0;
}

.history-icon {
  width: 16px;
  height: 16px;
  flex-shrink: 0;
  opacity: 0.7;
}

.history-text {
  font-size: 14px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.3;
  flex: 1;
}

.history-actions {
  opacity: 0;
  transition: opacity 0.2s ease;
}

.history-item:hover .history-actions {
  opacity: 1;
}

.action-btn {
  padding: 4px;
  background: transparent;
  border: none;
  color: #999999;
  cursor: pointer;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.action-btn:hover {
  background: #f5f5f5;
  color: #ff4d4f;
}

.action-btn svg {
  width: 14px;
  height: 14px;
}

/* 主内容区域 */
.main-content {
  flex: 1;
  background: #ffffff;
  height: 100%;
  position: relative;
  overflow: hidden;
}

/* 聊天标题栏 */
.chat-header {
  background: linear-gradient(135deg, #ffffff 0%, #fafafa 100%);
  border-bottom: 1px solid #e8e8e8;
  padding: 16px 24px;
  flex-shrink: 0;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.chat-title {
  display: flex;
  align-items: center;
  gap: 12px;
}

.title-icon {
  width: 20px;
  height: 20px;
  color: #1890ff;
}

.chat-title h1 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #333333;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 6px;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.status-dot.online {
  background: #52c41a;
  box-shadow: 0 0 8px rgba(82, 196, 26, 0.3);
}

.status-text {
  font-size: 12px;
  color: #666666;
  font-weight: 500;
}

/* 消息容器 */
.messages-container {
  height: calc(100% - 53px);
  overflow: hidden;
  /* display: flex; */
  /* flex: 1; */
  /* flex-direction: column; */
  position: relative;
}

.messages-wrapper {
  height: 100%;
  overflow-y: auto;
  padding: 24px 44px 80px 44px;
  /* 调整padding值确保头像与输入框对齐 */
  display: flex;
  flex-direction: column;
  gap: 16px;
  box-sizing: border-box;

  margin: 0 auto;
  /* 居中显示 */
  /* 隐藏滚动条但保持滚动功能 */
  scrollbar-width: none;
  /* Firefox */
  -ms-overflow-style: none;
  /* IE/Edge */
  /* 平滑滚动 */
  scroll-behavior: smooth;
}

/* 隐藏Webkit浏览器的滚动条 */
.messages-wrapper::-webkit-scrollbar {
  display: none;
}

/* 欢迎消息 */
.welcome-message {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300px;
  padding: 40px 20px;
}

.welcome-content {
  text-align: center;
  max-width: 500px;
}

.welcome-icon {
  width: 64px;
  height: 64px;
  margin: 0 auto 24px;
  padding: 16px;
  background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #ffffff;
}

.welcome-icon svg {
  width: 32px;
  height: 32px;
}

.welcome-title {
  font-size: 24px;
  font-weight: 600;
  color: #333333;
  margin: 0 0 16px 0;
}

.welcome-desc {
  font-size: 16px;
  color: #666666;
  line-height: 1.6;
  margin: 0;
}

/* 打字指示器 */
.typing-indicator {
  display: flex;
  justify-content: flex-start;
}

.typing-bubble {
  background: #f5f5f5;
  border-radius: 16px;
  padding: 12px 16px;
  border: 1px solid #e8e8e8;
}

.typing-dots {
  display: flex;
  gap: 4px;
}

.typing-dots span {
  width: 8px;
  height: 8px;
  background: #cccccc;
  border-radius: 50%;
  animation: typing 1.4s infinite ease-in-out;
}

.typing-dots span:nth-child(1) {
  animation-delay: -0.32s;
}

.typing-dots span:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes typing {

  0%,
  80%,
  100% {
    transform: scale(0.8);
    opacity: 0.5;
  }

  40% {
    transform: scale(1);
    opacity: 1;
  }
}

/* 输入区域 - ChatGPT风格悬浮输入框 */
.input-container {
  position: absolute;
  bottom: 16px;
  /* 减少底部距离，与消息间距更协调 */
  padding: 0 44px;
  left: 0;
  /* 紧贴左侧边栏，避免过多空白 */
  right: 0;
  /* 减少右侧距离 */
  background: transparent;
  /* 移除背景 */
  border: none;
  /* 移除边框 */
  box-shadow: none;
  /* 移除阴影 */
  z-index: 1000;
  backdrop-filter: none;
  /* 移除模糊效果 */
}

/* 移除渐变遮罩效果 */
.input-container::before {
  display: none;
}

.input-wrapper {
  /* 与消息区域宽度保持一致 */
  margin: 0 auto;
}

.input-box {
  display: flex;
  align-items: flex-end;
  background: #ffffff;
  border: 1px solid #d1d5db;
  border-radius: 26px;
  padding: 12px 16px;
  transition: all 0.2s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  min-height: 52px;
}

.input-box:focus-within {
  border-color: #9ca3af;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
}

/* 输入区域 */
.input-area {
  flex: 1;
  display: flex;
  align-items: center;
  padding-left: 4px;
}

.message-input {
  width: 100%;
  background: transparent;
  border: none;
  color: #374151;
  font-size: 16px;
  line-height: 1.5;
  outline: none;
  resize: none;
  min-height: 24px;
  max-height: 120px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.message-input::placeholder {
  color: #9ca3af;
}

/* 右侧功能按钮组 */
.right-actions {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-left: 12px;
}



/* 发送按钮 - ChatGPT风格 */
.send-button {
  background: #000000;
  border: none;
  border-radius: 50%;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.send-button:hover:not(:disabled) {
  background: #1f2937;
  transform: scale(1.05);
}

.send-button:disabled {
  background: #d1d5db;
  cursor: not-allowed;
  transform: none;
}

.send-icon {
  width: 16px;
  height: 16px;
  color: white;
}

.send-icon {
  width: 16px;
  height: 16px;
  color: #ffffff;
}

/* 左侧聊天历史滚动条样式 */
.chat-history::-webkit-scrollbar {
  width: 6px;
}

.chat-history::-webkit-scrollbar-track {
  background: transparent;
}

.chat-history::-webkit-scrollbar-thumb {
  background: #d9d9d9;
  border-radius: 3px;
}

.chat-history::-webkit-scrollbar-thumb:hover {
  background: #bfbfbf;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .sidebar {
    width: 100%;
    position: absolute;
    z-index: 100;
    transform: translateX(-100%);
    transition: transform 0.3s ease;
    height: 100vh;
    max-height: 100vh;
  }

  .sidebar.mobile-open {
    transform: translateX(0);
  }

  .main-content {
    width: 100%;
    height: 100%;
  }

  .messages-wrapper {
    padding: 20px 38px 90px 38px;
    /* 移动端调整padding值确保头像与输入框对齐 */
    max-width: 100%;
    /* 移动端全宽显示 */
    /* 确保移动端也隐藏滚动条 */
    scrollbar-width: none;
    -ms-overflow-style: none;
  }

  .messages-wrapper::-webkit-scrollbar {
    display: none;
  }

  .input-container {
    padding: 12px 16px 16px;
    /* 移动端输入框全宽显示 */
    left: 0;
    right: 0;
    /* 确保在移动端键盘弹起时输入框仍然可见 */
    bottom: env(safe-area-inset-bottom, 0);
  }

  .input-wrapper {
    max-width: 100%;
  }

  .input-box {
    padding: 10px 12px;
    border-radius: 18px;
    /* 移动端稍微减小圆角 */
  }

  .message-input {
    font-size: 16px;
    /* 防止iOS自动缩放 */
  }

  .send-button {
    width: 32px;
    height: 32px;
  }
}


</style>
