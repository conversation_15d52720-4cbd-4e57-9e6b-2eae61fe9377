<template>
  <el-card shadow="hover" class="course-card">
    <el-row
      type="flex"
      justify="space-between"
      align="stretch"
      class="card-content"
    >
      <!-- 左侧 -->
      <div class="left">
        <h4 class="course-title">
          <a
            :href="`/course/course_${course.courseId}.html`"
            target="_blank"
            rel="noopener noreferrer"
          >{{ course.title }}</a>
        </h4>

        <div class="subtitle">{{ course.subtitle }}</div>
      </div>

      <!-- 右侧 -->
      <div class="right">
        <div class="teacher-info">
          <img
            :src="course.teacherAvatar || defaultAvatar"
            class="avatar"
          />
          {{ course.teacher }}
        </div>

        <div class="right-bottom">
          <!-- 如果有draftLink直接用，没有则用draft字段做链接地址，没值则不显示 -->
          <el-button
            v-if="draftUrl"
            type="info"
            size="mini"
            @click="openDraft"
            class="draft-button"
          >
            底稿
          </el-button>

          <el-button
            type="primary"
            size="mini"
            :href="`/course/course_${course.courseId}.html`"
            target="_blank"
            rel="noopener noreferrer"
          >
            开始学习
          </el-button>
        </div>
      </div>
    </el-row>
  </el-card>
</template>

<script>
export default {
  name: "CourseCard",
  props: {
    course: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      defaultAvatar: "/images/default-avatar.png"
    };
  },
  computed: {
    draftUrl() {
      // 优先用course.draftLink，没有就拼一个路径用course.draft字段（假设它是路径或文件名）
      if (this.course.draftLink) {
        return this.course.draftLink;
      } else if (this.course.draft) {
        // 这里假设draft是相对路径或者文件名，你可以根据实际调整路径
        return `/drafts/${this.course.draft}`;
      } else {
        return null;
      }
    }
  },
  methods: {
    openDraft() {
      if (this.draftUrl) {
        window.open(this.draftUrl, '_blank', 'noopener,noreferrer');
      }
    }
  }
};
</script>

<style scoped>
.course-card {
  margin-bottom: 20px;
}

.card-content {
  display: flex;
  justify-content: space-between;
  align-items: stretch;
  min-height: 80px;
}

.left {
  flex: 1;
  min-width: 200px;
}

.course-title {
  margin: 0 0 10px 0;
  font-weight: 700;
  font-size: 22px; /* 正标题大一些 */
  color: #222;
}

.subtitle {
  color: #666;
  font-size: 18px; /* 副标题也大一些 */
  margin-left: 0; /* 副标题与主标题左对齐 */
  margin-bottom: 10px;
}

.right {
  flex: 0 0 200px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start; /* 改为顶部对齐 */
  align-items: flex-end;
}

.teacher-info {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #333;
  margin-top: 0; /* 与标题顶部对齐 */
  margin-bottom: auto; /* 自动向下推 */
}

.avatar {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  vertical-align: middle;
  margin-right: 5px;
}

.right-bottom {
  text-align: right;
  margin-top: auto; /* 推到底部 */
}

.draft-button {
  margin-right: 8px;
}

.right-bottom .el-button {
  margin-left: 0;
}
</style>
