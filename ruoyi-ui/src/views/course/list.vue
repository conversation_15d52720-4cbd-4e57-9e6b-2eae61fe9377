<template>
  <div class="app-container">
    <div class="course-container">
      <h2>课程列表</h2>

      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button
            type="primary"
            plain
            icon="el-icon-plus"
            size="mini"
            @click="handleAdd"
          >新增</el-button>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col
          :span="24"
          v-for="course in courseList"
          :key="course.courseId"
        >
          <el-card shadow="hover" class="course-card">
            <el-row type="flex" justify="space-between" align="stretch" class="card-content">

              <!-- 左 -->
              <div class="left">
                <h4 class="course-title">
                  <a :href="`/course/course_${course.courseId}.html`" target="_blank">{{ course.title }}</a>
                </h4>
                <div class="subtitle">{{ course.subtitle || '' }}</div>
                <div class="teacher-info">
                  <img
                    :src="course.teacherAvatar"
                    class="avatar"
                  />{{ course.teacher }}
                </div>
              </div>

              <!-- 右 -->
              <div class="right">
                <div class="card-footer">
                  <a
                    v-if="course.draft"
                    :href="`/drafts/${course.draft}`"
                    target="_blank"
                    rel="noopener noreferrer"
                    style="color: #409EFF; text-decoration: underline; margin-right: 10px;"
                  >
                    课程底稿
                  </a>
                  <el-button
                    type="warning"
                    size="mini"
                    @click="handleEdit(course)"
                  >
                    编辑
                  </el-button>
                  <el-button
                    type="danger"
                    size="mini"
                    @click="handleDelete(course.courseId)"
                  >
                    删除
                  </el-button>
                </div>
              </div>

            </el-row>
          </el-card>
        </el-col>
      </el-row>

      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getCourseList"
      />
    </div>

    <!-- 弹窗 -->
    <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="600px">
      <el-form :model="form" label-width="100px">
        <el-form-item label="课程标题">
          <el-input v-model="form.title"></el-input>
        </el-form-item>
        <el-form-item label="副标题">
          <el-input v-model="form.subtitle"></el-input>
        </el-form-item>
        <el-form-item label="课程底稿">
          <el-input v-model="form.draft"></el-input>
        </el-form-item>
        <el-form-item label="讲师">
          <el-input v-model="form.teacher"></el-input>
        </el-form-item>
        <el-form-item label="讲师头像">
          <el-input v-model="form.teacherAvatar"></el-input>
        </el-form-item>
        <el-form-item label="上课日期">
          <el-input v-model="form.date"></el-input>
        </el-form-item>
        <el-form-item label="学生人数">
          <el-input-number v-model="form.students" :min="0"></el-input-number>
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="form.status">
            <el-option label="正常" value="正常"></el-option>
            <el-option label="停用" value="停用"></el-option>
          </el-select>
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitForm">提交</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listCourse,
  addCourse,
  updateCourse,
  deleteCourse
} from "@/api/course";

export default {
  name: "CourseList",
  data() {
    return {
      courseList: [],
      total: 0,
      queryParams: {
        pageNum: 1,
        pageSize: 10
      },
      dialogVisible: false,
      dialogTitle: "新增课程",
      form: {
        courseId: null,
        title: "",
        subtitle: "",
        teacher: "",
        teacherAvatar: "",
        date: "",
        students: 0,
        status: "正常",
        draft: ""
      },
      isEdit: false
    };
  },
  created() {
    this.getCourseList();
  },
  methods: {
    /** 查询列表 */
    getCourseList() {
      listCourse(this.queryParams).then(res => {
        this.courseList = res.rows || [];
        this.total = res.total || 0;
      });
    },
    handleAdd() {
      this.dialogTitle = "新增课程";
      this.isEdit = false;
      this.form = {
        courseId: null,
        title: "",
        subtitle: "",
        teacher: "",
        teacherAvatar: "",
        date: "",
        students: 0,
        status: "正常",
        draft: ""
      };
      this.dialogVisible = true;
    },
    handleEdit(course) {
      this.dialogTitle = "编辑课程";
      this.isEdit = true;
      this.form = { ...course };
      this.dialogVisible = true;
    },
    handleDelete(id) {
      this.$confirm("确认删除该课程吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        deleteCourse(id).then(() => {
          this.$message.success("删除成功");
          this.getCourseList();
        });
      });
    },
    submitForm() {
      if (!this.form.title) {
        this.$message.warning("请输入课程标题");
        return;
      }
      const fn = this.isEdit ? updateCourse : addCourse;
      fn(this.form).then(() => {
        this.$message.success(this.isEdit ? "更新成功" : "新增成功");
        this.dialogVisible = false;
        this.getCourseList();
      });
    }
  }
};
</script>

<style scoped lang="scss">
.course-container {
  margin-top: 20px;
}

.course-card {
  margin-bottom: 20px;
  padding: 15px;
}

.card-content {
  display: flex;
  justify-content: space-between;
  align-items: stretch;
}

.left {
  flex: 1;
  min-width: 200px;
}

.course-title {
  margin: 0 0 10px 0;
  font-weight: 700;
  font-size: 22px;
  color: #222;
}

.subtitle {
  color: #666;
  font-size: 18px;
  margin-left: 2em;
  margin-bottom: 10px;
}

.teacher-info {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #333;
  margin-left: 10em;
  margin-top: 5px;
}

.avatar {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  margin-right: 5px;
}

.right {
  flex: 0 0 220px;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  align-items: flex-end;
}

.card-footer {
  text-align: right;
}

.card-footer .el-button {
  margin-left: 8px;
}
</style>
