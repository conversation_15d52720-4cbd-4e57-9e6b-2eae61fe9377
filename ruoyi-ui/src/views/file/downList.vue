<template>
  <div class="app-container">


    <el-table
      :data="fileList"
      style="width: 100%; margin-top: 20px"
    >
      <el-table-column prop="fileName" label="文件名称"/>
      <el-table-column prop="createTime" label="上传时间"/>
      <el-table-column prop="fileSize" label="文件大小"/>
      <el-table-column label="操作" width="200">
        <template slot-scope="scope">
          <el-button type="text" size="small" @click="downloadFile(scope.row)">
            下载
          </el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
import { getToken } from "@/utils/auth";
import { listFileInfo } from "@/api/fileInfo";

export default {
  name: "FileList",
  data() {
    return {
      fileList: [],
      uploadUrl: process.env.VUE_APP_BASE_API + "/system/fileInfo/upload",
      headers: {
        Authorization: "Bearer " + getToken(),
      },
    };
  },
  created() {
    this.getList();
  },
  methods: {
    getList() {
      listFileInfo().then((res) => {
        this.fileList = res.rows;
      });
    },
    downloadFile(row) {
          const fileName = encodeURIComponent(row.fileName);
          const url = `${process.env.VUE_APP_BASE_API}/system/fileInfo/download?fileName=${fileName}`;
          window.open(url);
        },
    },
  };
</script>
