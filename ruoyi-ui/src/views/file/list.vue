<template>
  <div class="app-container">
    <el-upload
      class="upload-demo"
      :action="uploadUrl"
      :headers="headers"
      :on-success="handleUploadSuccess"
      :on-error="handleUploadError"
      :before-upload="beforeUpload"
      :show-file-list="false"
      name="file"
    >
      <el-button size="small" type="primary">上传文件</el-button>
    </el-upload>

    <el-table
      :data="fileList"
      style="width: 100%; margin-top: 20px"
    >
      <el-table-column prop="fileName" label="文件名称"/>
      <el-table-column prop="createTime" label="上传时间"/>
      <el-table-column prop="fileSize" label="文件大小"/>
      <el-table-column label="操作" width="200">
        <template slot-scope="scope">
          <el-button type="text" size="small" @click="downloadFile(scope.row)">
            下载
          </el-button>
          <el-button type="text" size="small" @click="removeFile(scope.row)">
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
import { getToken } from "@/utils/auth";
import { listFileInfo, delFileInfo, addFileInfo } from "@/api/fileInfo";

export default {
  name: "FileList",
  data() {
    return {
      fileList: [],
      uploadUrl: process.env.VUE_APP_BASE_API + "/system/fileInfo/upload",
      headers: {
        Authorization: "Bearer " + getToken(),
      },
    };
  },
  created() {
    this.getList();
  },
  methods: {
    getList() {
      listFileInfo().then((res) => {
        this.fileList = res.rows;
      });
    },
    beforeUpload(file) {
      const isLt10M = file.size / 1024 / 1024 < 10;
      if (!isLt10M) {
        this.$message.error("文件大小不能超过10MB!");
      }
      return isLt10M;
    },
    handleUploadSuccess(res) {
      if (res.code !== 200) {
        this.$message.error("上传失败：" + res.msg);
        return;
      }
      this.$message.success("上传成功");

      const fileInfo = {
        fileName: res.data.fileName,
        filePath: res.data.filePath,
        fileSize: res.data.fileSize,
        fileUrl: res.data.fileUrl
      };

      // 调用 /system/file 保存记录
      addFileInfo(fileInfo).then(() => {
        this.$message.success("保存记录成功");
        this.getList();
      });
    },
    handleUploadError() {
      this.$message.error("上传失败");
    },
    //downloadFile(row) {
    //  const token = getToken();
    //  window.open(
    //    process.env.VUE_APP_BASE_API +
    //      `/system/fileInfo/download?fileName=${encodeURIComponent(row.fileName)}&token=${token}`
    //  );
    //}
    downloadFile(row) {
      const fileName = encodeURIComponent(row.fileName);
      const url = `${process.env.VUE_APP_BASE_API}/system/fileInfo/download?fileName=${fileName}`;
      window.open(url);
    },
    removeFile(row) {
      this.$confirm("确认删除文件吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        delFileInfo(row.fileId).then(() => {
          this.$message.success("删除成功");
          this.getList();
        });
      });
    },
  },
};
</script>
