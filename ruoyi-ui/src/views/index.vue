<template>
  <div class="app-container home">
    <div class="course-container">
      <h2>课程列表</h2>
      <el-row :gutter="20">
        <el-col
          :span="24"
          v-for="course in courseList"
          :key="course.courseId"
        >
          <CourseCard :course="course" />
        </el-col>
      </el-row>
      
      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getCourseList"
      />
    </div>
  </div>
</template>

<script>
import { listCourse } from "@/api/course";
import CourseCard from "@/views/course/index.vue";

export default {
  name: "Index",
  components: { CourseCard },
  data() {
    return {
      courseList: [],
      total: 0,
      queryParams: {
        pageNum: 1,
        pageSize: 10
      }
    };
  },
  created() {
    this.getCourseList();
  },
  methods: {
    getCourseList() {
      listCourse(this.queryParams).then(res => {
        this.courseList = (res.rows || []).map(item => ({
          ...item,
          subtitle: item.subtitle || "默认副标题",
          draftLink: item.draftLink || null
        }));
        this.total = res.total || 0;
      });
    }
  }
};
</script>

<style scoped lang="scss">
.home {
  display: block;
  min-height: 80vh;

  .course-container {
    margin-top: 20px;
  }
}
</style>
