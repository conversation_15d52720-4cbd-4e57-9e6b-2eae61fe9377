<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="账单日期" prop="billDate">
        <el-date-picker
          v-model="queryParams.billDateRange"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          @change="selectDateChange">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="凭证号" prop="voucherNo">
                    <el-input
                      v-model="queryParams.voucherNo"
                      placeholder="请输入凭证号"
                      clearable
                      @keyup.enter.native="handleQuery"
                    />
                  </el-form-item>
      <el-form-item label="账单方向" prop="billDirect">
        <el-input
          v-model="queryParams.billDirect"
          placeholder="请输入账单方向"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="一级科目" prop="firstlevel">
        <el-input
          v-model="queryParams.firstlevel"
          placeholder="请输入一级科目"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="二级科目" prop="secondlevel">
        <el-input
          v-model="queryParams.secondlevel"
          placeholder="请输入二级科目"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="三级科目" prop="thirdlevel">
        <el-input
          v-model="queryParams.thirdlevel"
          placeholder="请输入三级科目"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="核算项目" prop="accountproject">
        <el-input
          v-model="queryParams.accountproject"
          placeholder="请输入核算项目"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>


    <el-table v-loading="loading" :data="billList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="账单日期" align="center" prop="billDate"
              :formatter="(row) => formatDate(row.billDate)" />
      <el-table-column label="凭证号" align="center" prop="voucherNo" />
      <el-table-column label="账单方向" align="center" prop="billDirect" />
      <el-table-column label="一级科目" align="center" prop="firstlevel" />
      <el-table-column label="二级科目" align="center" prop="secondlevel" />
      <el-table-column label="三级科目" align="center" prop="thirdlevel" />
      <el-table-column label="核算项目" align="center" prop="accountproject" />
      <el-table-column label="发生额" align="center" prop="occurretamount" />
      <el-table-column label="余额" align="center" prop="balance" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
              <template slot-scope="scope">
              <el-button
                size="mini"
                type="text"
                icon="el-icon-check"
                @click="handleCheck(scope.row)"
               >检查</el-button>
               </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改账单对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="账单日期" prop="billDate">
        <el-date-picker
              v-model="form.billDate"
              type="date"
              placeholder="选择日期"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              @change="handleDateChange">
            </el-date-picker>
        </el-form-item>

        <el-form-item label="账单方向" prop="billDirect">
          <el-input v-model="form.billDirect" placeholder="请输入账单方向" />
        </el-form-item>
        <el-form-item label="一级科目" prop="firstlevel">
          <el-input v-model="form.firstlevel" placeholder="请输入一级科目" />
        </el-form-item>
        <el-form-item label="二级科目" prop="secondlevel">
          <el-input v-model="form.secondlevel" placeholder="请输入二级科目" />
        </el-form-item>
        <el-form-item label="三级科目" prop="thirdlevel">
          <el-input v-model="form.thirdlevel" placeholder="请输入三级科目" />
        </el-form-item>
        <el-form-item label="核算项目" prop="accountproject">
          <el-input v-model="form.accountproject" placeholder="请输入核算项目" />
        </el-form-item>
        <el-form-item label="发生额" prop="occurretamount">
          <el-input v-model="form.occurretamount" placeholder="请输入发生额" />
        </el-form-item>
        <el-form-item label="余额" prop="balance">
          <el-input v-model="form.balance" placeholder="请输入余额" />
        </el-form-item>
        <el-form-item label="备注" prop="notes">
          <el-input v-model="form.notes" type="textarea" placeholder="请输入内容" />
        </el-form-item>

      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
    <el-dialog
           title="图片查看"
           :visible.sync="imageViewerVisible"
           width="80%"
           top="5vh"
           append-to-body
           @closed="imageViewerVisible = false"
           class="image-viewer-dialog"
         >
           <el-carousel
             :initial-index="currentCarouselIndex"
             indicator-position="outside"
             height="70vh"
             arrow="always"
           >
             <el-carousel-item
               v-for="(img, index) in carouselImages"
               :key="index"
               class="carousel-item-wrapper"
             >
               <el-image
                 :src="img"
                 fit="contain"
                 class="full-size-image"
                 :preview-src-list="carouselImages"
               >
                 <div slot="error" class="image-slot">
                   <i class="el-icon-picture-outline"></i>
                   <span>图片加载失败</span>
                 </div>
               </el-image>
             </el-carousel-item>
           </el-carousel>
         </el-dialog>
  </div>
</template>

<script>
import { listBill, getBill, delBill, addBill, updateBill } from "@/api/system/bill";

export default {
  name: "Bill",
  data() {
    return {
    dateRange: [],
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 账单表格数据
      billList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      carouselImages: [], // 轮播图片列表
      currentCarouselIndex: 0, // 当前轮播索引
      imageViewerVisible: false, // 控制图片查看器显示
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        billDateRange: [],
        voucherNo: null,
        billDirect: null,
        firstlevel: null,
        secondlevel: null,
        thirdlevel: null,
        accountproject: null
        //occurretamount: null,
        //balance: null,
        //notes: null,
        //bak1: null,
        //bak2: null,
        //bak3: null,
        //bak4: null,
        //bak5: null,
        //doc1: null,
        //doc2: null,
        //doc3: null,
        //doc4: null,
        //doc5: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
  formatDate(dateValue) {
        if (!dateValue) return ""; // 空值处理

        // 情况1：如果是 Excel 序列号（如 45173）
        if (typeof dateValue === 'number') {
          const date = new Date(1899, 11, 30);
          date.setDate(date.getDate() + dateValue);
          return this.formatToYYYYMMDD(date);
        }

        // 情况2：如果是 Date 对象
        if (dateValue instanceof Date) {
          return this.formatToYYYYMMDD(dateValue);
        }

        // 情况3：如果是字符串（如 "9/1/23" 或 "2023/09/04"）
        if (typeof dateValue === 'string') {
          // 处理短格式 "9/1/23" → 转换为 Date 对象
          if (dateValue.match(/^\d{1,2}\/\d{1,2}\/\d{2}$/)) {
            const [month, day, shortYear] = dateValue.split('/');
            const fullYear = 2000 + parseInt(shortYear); // 假设 23 → 2023
            const date = new Date(fullYear, month - 1, day);
            return this.formatToYYYYMMDD(date);
          }
          // 处理其他字符串格式（如 "2023/09/04"）
          return dateValue.replace(/\//g, '-').replace(/(\d{4})-(\d{1,2})-(\d{1,2})/, (_, y, m, d) => {
            return `${y}-${m.padStart(2, '0')}-${d.padStart(2, '0')}`;
          });
        }

        return dateValue; // 其他情况原样显示
      },

      // 辅助函数：将 Date 对象格式化为 yyyy-MM-dd
      formatToYYYYMMDD(date) {
        const y = date.getFullYear();
        const m = String(date.getMonth() + 1).padStart(2, '0');
        const d = String(date.getDate()).padStart(2, '0');
        return `${y}-${m}-${d}`;
      },
    /** 查询账单列表 */
    getList() {
      this.loading = true;
      listBill(this.queryParams).then(response => {
        this.billList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    handleDateChange(val) {
          console.log('选择的日期区间:', val);
          // 这里可以处理日期区间的逻辑
        },
    selectDateChange(val) {
              console.log('选择的日期区间:', val);
              // 这里可以处理日期区间的逻辑
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        billId: null,
        billDate: null,
        billDirect: null,
        firstlevel: null,
        secondlevel: null,
        thirdlevel: null,
        accountproject: null,
        occurretamount: null,
        balance: null,
        notes: null,
        bak1: null,
        bak2: null,
        bak3: null,
        bak4: null,
        bak5: null,
        doc1: null,
        doc2: null,
        doc3: null,
        doc4: null,
        doc5: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    handleCheck(row) {
          // 假设 row.doc1, row.doc2 等是图片 URL
            const images = [];
            if(row.bak1!=null && row.bak1!='' && row.bak1!=undefined)
            {
              //images.push('/static/bill/' +row[`bak1`].replaceAll('/','-')+'.jpg');
              images.push('/static/bill/' +row[`bak1`]+'.jpg');
            }
            if(row.bak2!=null && row.bak2!='' && row.bak2!=undefined)
             {
               //images.push('/static/bill/' +row[`bak2`].replaceAll('/','-')+'.jpg');
               images.push('/static/bill/' +row[`bak2`]+'.jpg');
             }
             if(row.bak3!=null && row.bak3!='' && row.bak3!=undefined)
             {
               //images.push('/static/bill/' +row[`bak3`].replaceAll('/','-')+'.jpg');
               images.push('/static/bill/' +row[`bak3`]+'.jpg');
             }
             if(row.bak4!=null && row.bak4!='' && row.bak4!=undefined)
             {
                //images.push('/static/bill/' +row[`bak4`].replaceAll('/','-')+'.jpg');
                images.push('/static/bill/' +row[`bak4`]+'.jpg');
             }
             if(row.bak5!=null && row.bak5!='' && row.bak5!=undefined)
             {
                //images.push('/static/bill/' +row[`bak5`].replaceAll('/','-')+'.jpg');
                images.push('/static/bill/' +row[`bak5`]+'.jpg');
             }
            if (images.length === 0) {
              this.$message.warning("该账单没有关联图片");
              return;
            }
            //for (let i = 0; i < images.length; i++) {
            //console.log(images[i]);
            //}

            this.carouselImages = images;
            this.currentCarouselIndex = 0;
            this.imageViewerVisible = true;
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.billId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加账单";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const billId = row.billId || this.ids
      getBill(billId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改账单";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.billId != null) {
            updateBill(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addBill(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const billIds = row.billId || this.ids;
      this.$modal.confirm('是否确认删除账单编号为"' + billIds + '"的数据项？').then(function() {
        return delBill(billIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('system/bill/export', {
        ...this.queryParams
      }, `bill_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>

