<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="账单日期" prop="billDate">
        <el-date-picker
          v-model="queryParams.billDateRange"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          @change="selectDateChange">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="凭证号" prop="voucherNo">
              <el-input
                v-model="queryParams.voucherNo"
                placeholder="请输入凭证号"
                clearable
                @keyup.enter.native="handleQuery"
              />
            </el-form-item>
      <el-form-item label="账单方向" prop="billDirect">
        <el-input
          v-model="queryParams.billDirect"
          placeholder="请输入账单方向"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="一级科目" prop="firstlevel">
        <el-input
          v-model="queryParams.firstlevel"
          placeholder="请输入一级科目"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="二级科目" prop="secondlevel">
        <el-input
          v-model="queryParams.secondlevel"
          placeholder="请输入二级科目"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="三级科目" prop="thirdlevel">
        <el-input
          v-model="queryParams.thirdlevel"
          placeholder="请输入三级科目"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="核算项目" prop="accountproject">
        <el-input
          v-model="queryParams.accountproject"
          placeholder="请输入核算项目"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
      <el-button
                type="danger"
                plain
                icon="el-icon-edit"
                size="mini"
                @click="handleInit"
              >初始化</el-button>
       </el-col>

      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="billList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="账单日期" align="center" prop="billDate"
        :formatter="(row) => formatDate(row.billDate)" />
      <el-table-column label="凭证号" align="center" prop="voucherNo" />
      <el-table-column label="账单方向" align="center" prop="billDirect" />
      <el-table-column label="一级科目" align="center" prop="firstlevel" />
      <el-table-column label="二级科目" align="center" prop="secondlevel" />
      <el-table-column label="三级科目" align="center" prop="thirdlevel" />
      <el-table-column label="核算项目" align="center" prop="accountproject" />
      <el-table-column label="发生额" align="center" prop="occurretamount" />
      <el-table-column label="余额方向" align="center" prop="balanceDirect" />
      <el-table-column label="余额" align="center" prop="balance" />

      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
        <el-button
          size="mini"
          type="text"
          icon="el-icon-check"
          @click="handleCheck(scope.row)"
          v-hasPermi="['system:bill:edit']"
         >检查</el-button>
         <el-button
                     size="mini"
                     type="text"
                     icon="el-icon-edit"
                     @click="handleAddImg(scope.row)"
                     v-hasPermi="['system:bill:edit']"
                   >添加图片</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['system:bill:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['system:bill:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
<input v-show="justInit" type="file" @change="handleFileUpload" accept=".xlsx, .xls" />

    <!-- 添加或修改账单对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="账单日期" prop="billDate">
        <el-date-picker
              v-model="form.billDate"
              type="date"
              placeholder="选择日期"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              @change="handleDateChange">
            </el-date-picker>
        </el-form-item>
        <el-form-item label="凭证号" prop="voucherNo">
           <el-input v-model="form.voucherNo" placeholder="请输入凭证号" />
        </el-form-item>
        <el-form-item label="账单方向" prop="billDirect">
          <el-input v-model="form.billDirect" placeholder="请输入账单方向" />
        </el-form-item>
        <el-form-item label="一级科目" prop="firstlevel">
          <el-input v-model="form.firstlevel" placeholder="请输入一级科目" />
        </el-form-item>
        <el-form-item label="二级科目" prop="secondlevel">
          <el-input v-model="form.secondlevel" placeholder="请输入二级科目" />
        </el-form-item>
        <el-form-item label="三级科目" prop="thirdlevel">
          <el-input v-model="form.thirdlevel" placeholder="请输入三级科目" />
        </el-form-item>
        <el-form-item label="核算项目" prop="accountproject">
          <el-input v-model="form.accountproject" placeholder="请输入核算项目" />
        </el-form-item>
        <el-form-item label="发生额" prop="occurretamount">
          <el-input v-model="form.occurretamount" placeholder="请输入发生额" />
        </el-form-item>

        <el-form-item label="余额方向" prop="balanceDirect">
          <el-input v-model="form.balanceDirect" placeholder="请输入余额方向" />
        </el-form-item>
        <el-form-item label="余额" prop="balance">
          <el-input v-model="form.balance" placeholder="请输入余额" />
        </el-form-item>
        <el-form-item label="备注" prop="notes">
          <el-input v-model="form.notes" type="textarea" placeholder="请输入内容" />
        </el-form-item>

      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
    <!-- 添加图片查看器对话框 -->
     <!-- 图片轮播弹窗 -->
     <!-- 图片查看器对话框 -->
     <el-dialog
       title="图片查看"
       :visible.sync="imageViewerVisible"
       width="80%"
       top="5vh"
       append-to-body
       @closed="imageViewerVisible = false"
       class="image-viewer-dialog"
     >
       <el-carousel
         :initial-index="currentCarouselIndex"
         indicator-position="outside"
         height="70vh"
         arrow="always"
       >
         <el-carousel-item
           v-for="(img, index) in carouselImages"
           :key="index"
           class="carousel-item-wrapper"
         >
           <el-image
             :src="img"
             fit="contain"
             class="full-size-image"
             :preview-src-list="carouselImages"
           >
             <div slot="error" class="image-slot">
               <i class="el-icon-picture-outline"></i>
               <span>图片加载失败</span>
             </div>
           </el-image>
         </el-carousel-item>
       </el-carousel>
     </el-dialog>
     <el-dialog :title="Imgtitle" :visible.sync="openImg" width="700px" append-to-body>
           <el-form ref="imgform" :model="form" :rules="rules" label-width="80px">

             <el-form-item label="预览缩略图" prop="billImg" label-width="40">
                           <el-upload
                             :action="imgUpload.url"
                             :headers="imgUpload.headers"
                             list-type="picture-card"
                             :limit="limit"
                             :on-exceed="handleExceed"
                             :on-success="handlePictureSuccess"
                             :before-upload="beforeAvatarUpload"
                             :on-preview="handlePictureCardPreview"
                             :file-list="fileList"
                           >
                             <i class="el-icon-plus"></i>
                           </el-upload>
                           <el-dialog :visible.sync="dialogVisible">
                             <img width="100%" v-if="imageUrl" :src="imageUrl" alt="">
                           </el-dialog>
            </el-form-item>

           </el-form>
           <div slot="footer" class="dialog-footer">
             <el-button type="primary" @click="imgsubmitForm">确 定</el-button>
             <el-button @click="imgcancel">取 消</el-button>
           </div>
         </el-dialog>
  </div>
</template>

<script>
import { listBill, getBill, delBill, addBill, updateBill,updateBillImg } from "@/api/system/bill";
import axios from "axios";
import request from '@/utils/request';
import { getToken } from '@/utils/auth'; // 导入 getToken 方法
import * as XLSX from "xlsx";

export default {
  name: "Bill",
  data() {
    return {
    justInit: false,//只在初始化时显示
    dateRange: [],
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 账单表格数据
      billList: [],
      // 弹出层标题
      title: "",
      Imgtitle: "",
      // 是否显示弹出层
      open: false,
      openImg: false,
      rowid: null,
      rules: {
             billDate: [
          { required: true, message: "账单日期不能为空", trigger: "blur" }
        ],
        voucherNo: [
          { required: true, message: "凭证号不能为空", trigger: "blur" }
        ],
        billDirect: [
          { required: true, message: "账单方向不能为空", trigger: "blur" }
        ],
        firstlevel: [
          { required: true, message: "一级科目不能为空", trigger: "blur" }
        ],
        secondlevel: [
          { required: true, message: "二级科目不能为空", trigger: "blur" }
        ],
        thirdlevel: [
          { required: true, message: "三级科目不能为空", trigger: "blur" }
        ],
        accountproject: [
          { required: true, message: "核算项目不能为空", trigger: "blur" }
        ],
        occurretamount: [
          { required: true, message: "发生额不能为空", trigger: "blur" }
        ],
        balanceDirect: [
          { required: true, message: "余额方向不能为空", trigger: "blur" }
        ],
        balance: [
          { required: true, message: "余额不能为空", trigger: "blur" }
        ]
      },
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        billDateRange: [],
        voucherNo: null,
        billDirect: null,
        firstlevel: null,
        secondlevel: null,
        thirdlevel: null,
        accountproject: null
        //occurretamount: null,
        //balance: null,
        //notes: null,
        //bak1: null,
        //bak2: null,
        //bak3: null,
        //bak4: null,
        //bak5: null,
        //doc1: null,
        //doc2: null,
        //doc3: null,
        //doc4: null,
        //doc5: null
      },
      // 表单参数
      form: {},

      limit: 5,
     //页面上存的暂时图片地址List
     fileList: [],
     imgList: [],
     //图片地址
     imageUrl: "",
     dialogVisible: false,
     imgUpload: {
        // 设置上传的请求头部
        headers: {
           Authorization: "Bearer " + getToken()
         },
         // 图片上传的方法地址:
         url: process.env.VUE_APP_BASE_API + "/system/introductionjob/billImg",
      },
      imageViewerVisible: false, // 控制图片查看器显示
      carouselImages: [], // 轮播图片列表
      currentCarouselIndex: 0, // 当前轮播索引
      uploadExcelUrl: (process.env.VUE_APP_BASE_API || 'http://localhost:8080') + '/system/excel/upload', // 设置默认值
    };
  },
  created() {
    this.getList();
  },
  methods: {
  formatDate(dateValue) {
      if (!dateValue) return ""; // 空值处理

      // 情况1：如果是 Excel 序列号（如 45173）
      if (typeof dateValue === 'number') {
        const date = new Date(1899, 11, 30);
        date.setDate(date.getDate() + dateValue);
        return this.formatToYYYYMMDD(date);
      }

      // 情况2：如果是 Date 对象
      if (dateValue instanceof Date) {
        return this.formatToYYYYMMDD(dateValue);
      }

      // 情况3：如果是字符串（如 "9/1/23" 或 "2023/09/04"）
      if (typeof dateValue === 'string') {
        // 处理短格式 "9/1/23" → 转换为 Date 对象
        if (dateValue.match(/^\d{1,2}\/\d{1,2}\/\d{2}$/)) {
          const [month, day, shortYear] = dateValue.split('/');
          const fullYear = 2000 + parseInt(shortYear); // 假设 23 → 2023
          const date = new Date(fullYear, month - 1, day);
          return this.formatToYYYYMMDD(date);
        }
        // 处理其他字符串格式（如 "2023/09/04"）
        return dateValue.replace(/\//g, '-').replace(/(\d{4})-(\d{1,2})-(\d{1,2})/, (_, y, m, d) => {
          return `${y}-${m.padStart(2, '0')}-${d.padStart(2, '0')}`;
        });
      }

      return dateValue; // 其他情况原样显示
    },

    // 辅助函数：将 Date 对象格式化为 yyyy-MM-dd
    formatToYYYYMMDD(date) {
      const y = date.getFullYear();
      const m = String(date.getMonth() + 1).padStart(2, '0');
      const d = String(date.getDate()).padStart(2, '0');
      return `${y}-${m}-${d}`;
    },
  /** 检查按钮操作 - 查看图片 */
    handleCheck(row) {
      // 假设 row.doc1, row.doc2 等是图片 URL
        const images = [];
        if(row.bak1!=null && row.bak1!='' && row.bak1!=undefined)
        {
          //images.push('/static/bill/' +row[`bak1`].replaceAll('/','-')+'.jpg');
          images.push('/static/bill/' +row[`bak1`]+'.jpg');
        }
        if(row.bak2!=null && row.bak2!='' && row.bak2!=undefined)
         {
           //images.push('/static/bill/' +row[`bak2`].replaceAll('/','-')+'.jpg');
           images.push('/static/bill/' +row[`bak2`]+'.jpg');
         }
         if(row.bak3!=null && row.bak3!='' && row.bak3!=undefined)
         {
           //images.push('/static/bill/' +row[`bak3`].replaceAll('/','-')+'.jpg');
           images.push('/static/bill/' +row[`bak3`]+'.jpg');
         }
         if(row.bak4!=null && row.bak4!='' && row.bak4!=undefined)
         {
            //images.push('/static/bill/' +row[`bak4`].replaceAll('/','-')+'.jpg');
            images.push('/static/bill/' +row[`bak4`]+'.jpg');
         }
         if(row.bak5!=null && row.bak5!='' && row.bak5!=undefined)
         {
            //images.push('/static/bill/' +row[`bak5`].replaceAll('/','-')+'.jpg');
            images.push('/static/bill/' +row[`bak5`]+'.jpg');
         }


        if (images.length === 0) {
          this.$message.warning("该账单没有关联图片");
          return;
        }
        for (let i = 0; i < images.length; i++) {
        console.log(images[i]);
        }

        this.carouselImages = images;
        this.currentCarouselIndex = 0;
        this.imageViewerVisible = true;
    },
  handleFileUpload(event) {
        const file = event.target.files[0];
        if (!file) return;

        const reader = new FileReader();
        reader.readAsBinaryString(file);
        reader.onload = (e) => {
          const binaryData = e.target.result;
          const workbook = XLSX.read(binaryData, { type: "binary" });

          // 读取第一个工作表
          const sheetName = workbook.SheetNames[0];
          const sheet = workbook.Sheets[sheetName];

          // 将数据转换为二维数组
          const data = XLSX.utils.sheet_to_json(sheet, {
            header: 1,
            raw: false,          // 强制解析为文本或日期（而不是原始数字）
            dateNF: "yyyy-mm-dd", // 指定日期格式
          });

          // 提取每个数组的前11条数据
          const extractedData = data.map(row => row.slice(0, 15));

              // 遍历输出结果
          extractedData.forEach((row, index) => {
          if(index>0)
          {//console.log('row',row);
          this.form = {};
          this.form.billDate = row[0];
          this.form.voucherNo = row[1];
          this.form.billDirect = row[2];
          this.form.firstlevel = row[3];
          this.form.secondlevel = row[4];
          this.form.thirdlevel = row[5];
          this.form.accountproject = row[6];
          this.form.occurretamount = row[7];
          this.form.balanceDirect= row[8];
          this.form.balance = row[9];
          this.form.notes = row[10];
          this.form.bak1 = row[11];
          this.form.bak2 = row[12];
          this.form.bak3 = row[13];
          this.form.bak4 = row[14];

          addBill(this.form).then(response => {});
          }
          });
          this.$modal.msgSuccess("初始化成功");
          this.getList();
        };
      },
  handleInit() {
    this.justInit = !this.justInit;
  },
    /** 查询账单列表 */
    getList() {
      this.loading = true;
      listBill(this.queryParams).then(response => {
        this.billList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    handleAddImg(row) {
    console.log("row=",row);
    this.rowid = row.billId;
    this.fileList = [];
    this.imgList = [];
    if(row.bak1!=null && row.bak1!='' && row.bak1!=undefined)
    {
    this.imgList.push(row.bak1);
      this.fileList.push({ url: '/static/bill/' + row.bak1+".jpg", name: '图片1' });
    }
    if(row.bak2!=null && row.bak2!='' && row.bak2!=undefined)
    {
    this.imgList.push(row.bak2);
    this.fileList.push({ url: '/static/bill/' + row.bak2+".jpg", name: '图片2' });
    }
    if(row.bak3!=null && row.bak3!='' && row.bak3!=undefined)
    {
    this.imgList.push(row.bak3);
       this.fileList.push({ url: '/static/bill/' + row.bak3+".jpg", name: '图片3' });
    }
    if(row.bak4!=null && row.bak4!='' && row.bak4!=undefined)
    {
    this.imgList.push(row.bak4);
       this.fileList.push({ url: '/static/bill/' + row.bak4+".jpg", name: '图片4' });
    }
    if(row.bak5!=null && row.bak5!='' && row.bak5!=undefined)
    {
    this.imgList.push(row.bak5);
      this.fileList.push({ url: '/static/bill/' + row.bak5+".jpg", name: '图片5' });
    }
    this.openImg = true;
    this.Imgtitle = "添加图片";
    },
    handleDateChange(val) {
          console.log('选择的日期区间:', val);
          // 这里可以处理日期区间的逻辑
        },
    selectDateChange(val) {
              console.log('选择的日期区间:', val);
              // 这里可以处理日期区间的逻辑
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    imgcancel() {
      this.openImg = false;
    },
    imgsubmitForm() {

    this.form = {};
    this.form.billId = this.rowid;
     this.form.bak1 = '';
     this.form.bak2 = '';
     this.form.bak3 = '';
     this.form.bak4 = '';
     this.form.bak5 = '';
    for (var i = 0; i < this.imgList.length; i++) {
    let temp=this.imgList[i];
    //console.log("temp=",temp);
    temp=temp.replace('/bill/','#');
    const fruits =temp.split('#');

    let billfileName='';
    if(fruits.length>1)
    {
     billfileName=fruits[1];
    }
    else{
    billfileName=this.imgList[i];
    }
    billfileName=billfileName.replace('.jpg','');
      if(i==0)
      {
       this.form.bak1 = billfileName;
      }
      else if(i==1)
      {
        this.form.bak2 = billfileName;
      }
      else if(i==2)
      {
         this.form.bak3 = billfileName;
      }
      else if(i==3)
      {
         this.form.bak4 = billfileName;
      }
      else if(i==4)
      {
        this.form.bak5 = billfileName;
      }
    }

    updateBillImg(this.form).then(response => {
    this.$modal.msgSuccess("修改成功");
       this.openImg = false;
       });
    },
    // 表单重置
    reset() {
      this.form = {
        billId: null,
        billDate: null,
        voucherNo: null,
        billDirect: null,
        firstlevel: null,
        secondlevel: null,
        thirdlevel: null,
        accountproject: null,
        occurretamount: null,
        balance: null,
        notes: null,
        bak1: null,
        bak2: null,
        bak3: null,
        bak4: null,
        bak5: null,
        doc1: null,
        doc2: null,
        doc3: null,
        doc4: null,
        doc5: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.billId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加账单";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const billId = row.billId || this.ids
      getBill(billId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改账单";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.billId != null) {
            updateBill(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addBill(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const billIds = row.billId || this.ids;
      this.$modal.confirm('是否确认删除账单编号为"' + billIds + '"的数据项？').then(function() {
        return delBill(billIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    //图片上传前的相关判断
        beforeAvatarUpload(file) {
          const isJPG = file.type === 'image/jpeg' || file.type == 'image/png';
          const isLt2M = file.size / 1024 / 1024 < 5;
          if (!isJPG) {
            this.$message.error('上传头像图片只能是 JPG/PNG 格式!');
          }
          if (!isLt2M) {
            this.$message.error('上传头像图片大小不能超过 5MB!');
          }
          return isJPG && isLt2M;
        },
        //图片预览
        handlePictureCardPreview(file) {
        //console.log("file.url=",file.url);
          this.imageUrl = file.url;
          this.dialogVisible = true;
        },
        //图片上传成功后的回调
        handlePictureSuccess(res, file) {
          //设置图片访问路径 （billImg 后台传过来的的上传地址）
          this.imageUrl = file.response.billImg;
          let l=file.response.articleImg;
          this.imgList.push(l);
          //console.log("this.uploadimageUrl=",this.imageUrl);
          //console.log("res=",res);
          //console.log("file.response.articleImg=",file.response.articleImg);
        },
        // 文件个数超出
        handleExceed() {
          this.$modal.msgError(`上传链接LOGO图片数量不能超过 ${this.limit} 个!`);
        },
    /** 导出按钮操作 */
    handleExport() {
      this.download('system/bill/export', {
        ...this.queryParams
      }, `bill_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>

