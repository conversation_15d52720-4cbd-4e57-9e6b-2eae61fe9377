<template>
  <div id="app">
    <h1>Excel Data</h1>
    <input type="file" @change="handleFileUpload" />
    <table v-if="data.length > 0">
      <tr v-for="(row, rowIndex) in data" :key="rowIndex">
        <td v-for="(cell, cellIndex) in row" :key="cellIndex">
          <input v-model="data[rowIndex][cellIndex]" />
        </td>
      </tr>
    </table>
    <button @click="saveData">Save</button>
  </div>
</template>

<script>
import axios from 'axios';

export default {
  data() {
    return {
      data: [],
    };
  },
  methods: {
    handleFileUpload(event) {
      const file = event.target.files[0];
      const formData = new FormData();
      formData.append('file', file);

      axios.post('/api/excel/upload', formData)
        .then(response => {
          this.data = response.data;
          console.log('this.data',this.data);
        })
        .catch(error => {
          console.error('Error uploading file:', error);
        });
    },
    saveData() {
      axios.post('/api/excel/save', this.data)
        .then(response => {
          alert('Data saved successfully!');
        })
        .catch(error => {
          console.error('Error saving data:', error);
        });
    },
  },
};
</script>

<style>
table {
  border-collapse: collapse;
  width: 100%;
}
td {
  border: 1px solid #000;
  padding: 8px;
}
input {
  width: 100%;
}
</style>
