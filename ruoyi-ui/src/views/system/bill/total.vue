<template>
  <div class="app-container">

    <el-table  v-loading="loading" :data="billList" @selection-change="handleSelectionChange" style="width: 100%">
      <el-table-column label="账单日期" align="center" prop="billDate"
              :formatter="(row) => formatDate(row.billDate)" />
      <el-table-column label="凭证号" align="center" prop="voucherNo" />
      <el-table-column label="账单方向" align="center" prop="billDirect" />
      <el-table-column label="一级科目" align="center" prop="firstlevel" />
      <el-table-column label="二级科目" align="center" prop="secondlevel" />
      <el-table-column label="三级科目" align="center" prop="thirdlevel" />
      <el-table-column label="核算项目" align="center" prop="accountproject" />
      <el-table-column label="发生额" align="center" prop="occurretamount" />
      <el-table-column label="余额" align="center" prop="balance" />
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />


  </div>
</template>

<script>
import { listBill, getBill, delBill, addBill, updateBill } from "@/api/system/bill";

export default {
  name: "Bill",
  data() {
    return {
    dateRange: [],
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 账单表格数据
      billList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        billDateRange: [],
        billDirect: null,
        firstlevel: null,
        secondlevel: null,
        thirdlevel: null,
        accountproject: null
        //occurretamount: null,
        //balance: null,
        //notes: null,
        //bak1: null,
        //bak2: null,
        //bak3: null,
        //bak4: null,
        //bak5: null,
        //doc1: null,
        //doc2: null,
        //doc3: null,
        //doc4: null,
        //doc5: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
  formatDate(dateValue) {
        if (!dateValue) return ""; // 空值处理

        // 情况1：如果是 Excel 序列号（如 45173）
        if (typeof dateValue === 'number') {
          const date = new Date(1899, 11, 30);
          date.setDate(date.getDate() + dateValue);
          return this.formatToYYYYMMDD(date);
        }

        // 情况2：如果是 Date 对象
        if (dateValue instanceof Date) {
          return this.formatToYYYYMMDD(dateValue);
        }

        // 情况3：如果是字符串（如 "9/1/23" 或 "2023/09/04"）
        if (typeof dateValue === 'string') {
          // 处理短格式 "9/1/23" → 转换为 Date 对象
          if (dateValue.match(/^\d{1,2}\/\d{1,2}\/\d{2}$/)) {
            const [month, day, shortYear] = dateValue.split('/');
            const fullYear = 2000 + parseInt(shortYear); // 假设 23 → 2023
            const date = new Date(fullYear, month - 1, day);
            return this.formatToYYYYMMDD(date);
          }
          // 处理其他字符串格式（如 "2023/09/04"）
          return dateValue.replace(/\//g, '-').replace(/(\d{4})-(\d{1,2})-(\d{1,2})/, (_, y, m, d) => {
            return `${y}-${m.padStart(2, '0')}-${d.padStart(2, '0')}`;
          });
        }

        return dateValue; // 其他情况原样显示
      },

      // 辅助函数：将 Date 对象格式化为 yyyy-MM-dd
      formatToYYYYMMDD(date) {
        const y = date.getFullYear();
        const m = String(date.getMonth() + 1).padStart(2, '0');
        const d = String(date.getDate()).padStart(2, '0');
        return `${y}-${m}-${d}`;
      },
    /** 查询账单列表 */
    getList() {
      this.loading = true;
      listBill(this.queryParams).then(response => {
        this.billList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    handleDateChange(val) {
          console.log('选择的日期区间:', val);
          // 这里可以处理日期区间的逻辑
        },
    selectDateChange(val) {
              console.log('选择的日期区间:', val);
              // 这里可以处理日期区间的逻辑
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        billId: null,
        billDate: null,
        billDirect: null,
        firstlevel: null,
        secondlevel: null,
        thirdlevel: null,
        accountproject: null,
        occurretamount: null,
        balance: null,
        notes: null,
        bak1: null,
        bak2: null,
        bak3: null,
        bak4: null,
        bak5: null,
        doc1: null,
        doc2: null,
        doc3: null,
        doc4: null,
        doc5: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.billId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加账单";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const billId = row.billId || this.ids
      getBill(billId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改账单";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.billId != null) {
            updateBill(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addBill(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const billIds = row.billId || this.ids;
      this.$modal.confirm('是否确认删除账单编号为"' + billIds + '"的数据项？').then(function() {
        return delBill(billIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('system/bill/export', {
        ...this.queryParams
      }, `bill_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>

