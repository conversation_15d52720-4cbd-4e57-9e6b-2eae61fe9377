<template>
  <div>
    <ExcelViewer :id="id" />
  </div>
</template>

<script>
//import ExcelViewer from "@/components/StyleExcelViewer.vue";
import ExcelViewer from "@/components/ComplexExcelViewerDetail.vue";


export default {
  data() {
    return {
      id: ''
    };
  },
  components: {
    ExcelViewer,
  },
  created() {
  this.id = this.$route.params.id;
  console.log("this.ida=",this.id);
  this.init();
  },
  methods: {
    init() {
    }
  }
};
</script>
