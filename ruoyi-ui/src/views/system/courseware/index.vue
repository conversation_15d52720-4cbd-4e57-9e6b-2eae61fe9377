<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="课件名称" prop="excelName">
        <el-input
          v-model="queryParams.excelName"
          placeholder="请输入课件名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="infoList" @selection-change="handleSelectionChange">
      <el-table-column  type="selection" width="55" align="center" />
      <el-table-column v-if="showFormItem" label="infoId" align="center" prop="infoId" />
      <el-table-column label="课件名称" align="center" prop="excelName" />
      <el-table-column label="路径" align="center" prop="excelPath" />
      <el-table-column label="备注" align="center" prop="excelRemark" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['system:info:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['system:info:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改【请填写功能名称】对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="课件名称" prop="excelName">
          <el-input v-model="form.excelName" placeholder="请输入课件名称" />
        </el-form-item>
        <el-upload v-show="justAdd"
              class="upload-demo"
              action=""
              :show-file-list="false"
              :before-upload="handleUpload">
              <el-button type="primary">上传模板</el-button>
        </el-upload>
        <br/>
        <el-form-item v-show="showFormItem" label="路径" prop="excelPath">
          <el-input v-model="form.excelPath" placeholder="请输入路径" />
        </el-form-item>
        <el-form-item  v-show="showFormItem"  label="备注" prop="excelRemark">
          <el-input v-model="form.excelRemark" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listInfo, getInfo, delInfo, addInfo, updateInfo } from "@/api/system/info";
import axios from "axios";
import request from '@/utils/request';
import { getToken } from '@/utils/auth'; // 导入 getToken 方法

export default {
  name: "Info",
  data() {
    return {
    justAdd: true,//上传按钮只在添加时显示
      showFormItem: false,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 【请填写功能名称】表格数据
      infoList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        excelName: null,
        excelPath: null,
        excelRemark: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      },
      baseUrl: process.env.VUE_APP_BASE_API || 'http://localhost:8080', // 设置默认值
      uploadExcelUrl: (process.env.VUE_APP_BASE_API || 'http://localhost:8080') + '/system/excel/upload', // 设置默认值
      headers: {
               Authorization: 'Bearer ' + getToken(), // 确保 getToken 可用
          }
    };
  },
  created() {
    this.getList();
  },
  methods: {

    /** 查询【请填写功能名称】列表 */
    getList() {
      this.loading = true;
      listInfo(this.queryParams).then(response => {
        this.infoList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        infoId: null,
        excelName: null,
        excelPath: null,
        excelRemark: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.infoId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
    this.justAdd=true;
      this.reset();
      this.open = true;
      this.title = "添加";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
    this.justAdd=false;
      this.reset();
      const infoId = row.infoId || this.ids
      getInfo(infoId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.infoId != null) {
            updateInfo(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addInfo(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const infoIds = row.infoId || this.ids;
      this.$modal.confirm('是否确认删除编号为"' + infoIds + '"的数据项？').then(function() {
        return delInfo(infoIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('system/info/export', {
        ...this.queryParams
      }, `info_${new Date().getTime()}.xlsx`)
    },
    async handleUpload(file) {
          const formData = new FormData();
            formData.append('file', file); // 'file' 是后端接收文件的参数名

            // 使用 axios 上传文件
            axios
              .post(this.uploadExcelUrl, formData, {
                headers: {
                  'Content-Type': 'multipart/form-data', // 设置请求头
                  Authorization: 'Bearer ' + getToken(), // 携带 Token
                },
              })
              .then((response) => {
                if (response.data.code === 200) {
                this.form.excelPath = response.data.data; // 表头
                this.tableHeaders = response.data.headers; // 表头
                console.log(response.data);
                  this.$message.success('文件上传成功');
                } else {
                  this.$message.error('文件上传失败');
                }
              })
              .catch((error) => {
                this.$message.error('文件上传失败');
                console.error(error);
              });

            return false; // 阻止 el-upload 自动上传
        }
  }
};
</script>
