<template>
  <div>
    <el-upload
      class="upload-demo"
      action=""
      :show-file-list="false"
      :before-upload="handleUpload">
      <el-button type="primary">上传 Excel 文件</el-button>
    </el-upload>

    <el-table :data="tableData" border style="width: 100%">
      <el-table-column v-for="(col, index) in columns" :key="index" :prop="col" :label="col" />
    </el-table>
  </div>
</template>

<script>
import * as XLSX from "xlsx";
import axios from "axios";
import request from '@/utils/request';
import { getToken } from '@/utils/auth'; // 导入 getToken 方法

export default {
  data() {
    return {
      tableData: [],
      tableHeaders: [], // 表头数据
      columns: [],
      baseUrl: process.env.VUE_APP_BASE_API || 'http://localhost:8080', // 设置默认值
      uploadExcelUrl: (process.env.VUE_APP_BASE_API || 'http://localhost:8080') + '/system/excel/upload', // 设置默认值
      headers: {
         Authorization: 'Bearer ' + getToken(), // 确保 getToken 可用
    }
  }},
  methods: {
    async handleUpload(file) {
      const formData = new FormData();
        formData.append('file', file); // 'file' 是后端接收文件的参数名

        // 使用 axios 上传文件
        axios
          .post(this.uploadExcelUrl, formData, {
            headers: {
              'Content-Type': 'multipart/form-data', // 设置请求头
              Authorization: 'Bearer ' + getToken(), // 携带 Token
            },
          })
          .then((response) => {
            if (response.data.code === 200) {
            this.tableHeaders = response.data.headers; // 表头
            console.log(this.tableHeaders);
              this.tableData = response.data.rows; // 表格数据
              this.processExcelData(response.data.data); // 假设后端返回解析后的数据
              this.$message.success('文件上传成功');
            } else {
              this.$message.error('文件上传失败');
            }
          })
          .catch((error) => {
            this.$message.error('文件上传失败');
            console.error(error);
          });

        return false; // 阻止 el-upload 自动上传
    },
    processExcelData(data) {
      if (data.length > 0) {
        this.columns = Object.keys(data[0]);
        this.tableData = data;
      }
    }
  }
};
</script>

<style>
.upload-demo {
  margin-bottom: 20px;
}
</style>
