<template>
  <div class="image-gallery">
    <div v-for="(image, index) in images" :key="index" class="image-item">
      <img :src="image.src" :alt="image.alt" class="gallery-image" />
      <p class="image-caption">{{ image.alt }}</p>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ImageGallery',
  props: {
    images: {
      type: Array,
      required: true
    }
  }
};
</script>

<style scoped>
.image-gallery {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 20px;
}

.image-item {
  text-align: center;
}

.gallery-image {
  max-width: 100%;
  height: auto;
  border-radius: 8px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.image-caption {
  margin-top: 10px;
  font-size: 0.9em;
  color: #666;
}
</style>
