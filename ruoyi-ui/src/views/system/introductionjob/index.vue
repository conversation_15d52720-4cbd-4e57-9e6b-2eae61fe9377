<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="标题" prop="menuId">
        <el-input
          v-model="queryParams.intrRemark"
          placeholder="标题"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['system:introductionjob:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['system:introductionjob:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:introductionjob:remove']"
        >删除</el-button>
      </el-col>
      <!-----<el-col :span="1.5">
                     <el-button
                       type="warning"
                       plain
                       icon="el-icon-download"
                       size="mini"
                       @click="handleExport"
                       v-hasPermi="['system:introductionjob:export']"
                     >导出</el-button>
                   </el-col>----->
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="introductionjobList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="序号" align="center" prop="intrId" width="50"/>
      <el-table-column label="标题" align="center" prop="intrRemark" width="150"/>
      <el-table-column label="介绍" align="center" prop="intrContent" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
        <el-button
                    size="mini"
                    type="text"
                    icon="el-icon-edit"
                    @click="goToAbout(scope.row.intrId)"
                    v-hasPermi="['system:introductionjob:detail']"
          >详情</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['system:introductionjob:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['system:introductionjob:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改项目介绍信息对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="700px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="项目标题" prop="intrRemark">
            <el-input v-model="form.intrRemark" type="textarea" placeholder="标题" />
         </el-form-item>
        <el-form-item label="介绍内容">
          <editor v-model="form.intrContent" :min-height="192"/>
        </el-form-item>
        <el-form-item label="预览缩略图" prop="articleImg" label-width="40">
                      <el-upload
                        :action="imgUpload.url"
                        :headers="imgUpload.headers"
                        list-type="picture-card"
                        :limit="limit"
                        :on-exceed="handleExceed"
                        :on-success="handlePictureSuccess"
                        :before-upload="beforeAvatarUpload"
                        :on-preview="handlePictureCardPreview"
                        :file-list="fileList"
                      >
                        <i class="el-icon-plus"></i>
                      </el-upload>
                      <el-dialog :visible.sync="dialogVisible">
                        <img width="100%" v-if="imageUrl" :src="imageUrl" alt="">
                      </el-dialog>
       </el-form-item>

      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listIntroductionjob, getIntroductionjob, delIntroductionjob, addIntroductionjob, updateIntroductionjob } from "@/api/system/introductionjob";
import { getToken } from "@/utils/auth";
import { introductionpath } from '@/views/system/introductionjob';

export default {
  name: "Introductionjob",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 项目介绍信息表格数据
      introductionjobList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        menuId: null,
        intrContent: null,
        imageData: null,
        intrRemark: null
      },
      // 表单参数
      form: {},
      //上传文件
      imageFile: null,
      // 表单校验
      rules: {
        menuId: [
          { required: true, message: "菜单", trigger: "blur" }
        ],
        intrContent: [
          { required: true, message: "介绍内容不能为空", trigger: "blur" }
        ],
      },
      limit: 1,
      　　　　//页面上存的暂时图片地址List
       fileList: [{url: ""}],
      　　　　//图片地址
       imageUrl: "",
       dialogVisible: false,
       imgUpload: {
                // 设置上传的请求头部
                headers: {
                  Authorization: "Bearer " + getToken()
                },
                // 图片上传的方法地址:
                url: process.env.VUE_APP_BASE_API + "/system/introductionjob/articleImg",
       }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询项目介绍信息列表 */
    getList() {
      this.loading = true;
      listIntroductionjob(this.queryParams).then(response => {
        this.introductionjobList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    handleFileChange(event) {
          this.imageFile.value  = event.target.files[0];
          //将文件数据读入imageData中，提交
          if (imageFile.value) {
             form.append('imageData', imageFile.value);
          }
     },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        intrId: null,
        menuId: null,
        intrContent: null,
        imageData: null,
        intrRemark: null
      };
      this.fileList = undefined;
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.queryParams.intrRemark='';
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.intrId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
    //alert(process.env.VUE_APP_BASE_API);
      this.reset();
      this.open = true;
      this.title = "添加项目介绍信息";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const intrId = row.intrId || this.ids
      getIntroductionjob(intrId).then(response => {
      this.fileList = [{ url: process.env.VUE_APP_BASE_API + response.data.imageData}];
        this.form = response.data;
        this.open = true;
        this.title = "修改项目介绍信息";
      });
    },
    goToAbout(nid) {
    this.$router.push({path:'/introductionjob/introduction',query:{id:nid}});
    },
    /** 提交按钮 */
    submitForm() {
    this.form.articleImg = this.imageUrl; // 注：重要(用于添加到数据库)
    this.form.imageData = this.imageUrl;
      this.$refs["form"].validate(valid => {
      console.log('上传成功:', this.form);
        if (valid) {
          if (this.form.intrId != null) {
            updateIntroductionjob(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addIntroductionjob(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    //图片上传前的相关判断
    beforeAvatarUpload(file) {
      const isJPG = file.type === 'image/jpeg' || file.type == 'image/png';
      const isLt2M = file.size / 1024 / 1024 < 5;
      if (!isJPG) {
        this.$message.error('上传头像图片只能是 JPG/PNG 格式!');
      }
      if (!isLt2M) {
        this.$message.error('上传头像图片大小不能超过 5MB!');
      }
      return isJPG && isLt2M;
    },
    //图片预览
    handlePictureCardPreview(file) {
      this.imageUrl = file.url;
      this.dialogVisible = true;
    },
    //图片上传成功后的回调
    handlePictureSuccess(res, file) {
      //设置图片访问路径 （articleImg 后台传过来的的上传地址）
      this.imageUrl = file.response.articleImg;
    },
    // 文件个数超出
    handleExceed() {
      this.$modal.msgError(`上传链接LOGO图片数量不能超过 ${this.limit} 个!`);
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const intrIds = row.intrId || this.ids;
      this.$modal.confirm('是否确认删除项目介绍信息编号为"' + intrIds + '"的数据项？').then(function() {
        return delIntroductionjob(intrIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('system/introductionjob/export', {
        ...this.queryParams
      }, `introductionjob_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
