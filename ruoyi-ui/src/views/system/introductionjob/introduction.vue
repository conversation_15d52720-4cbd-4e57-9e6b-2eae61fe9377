<template>
  <div class="audit-detail-container">
    <!-- 左侧项目信息区域 -->
    <div class="left-panel">
      <div class="project-header">
        <div class="project-icon">
          <i class="el-icon-document"></i>
        </div>
        <h1 class="project-title">{{ projectName }}</h1>
        <div class="project-tags">
          <el-tag size="small" type="info">审计项目</el-tag>
          <el-tag size="small" type="success">进行中</el-tag>
        </div>
      </div>

      <!-- 项目及项目组情况 -->
      <div class="project-info-section">
        <h3 class="section-title">
          <i class="el-icon-user"></i>
          项目及项目组情况
        </h3>
        <div class="info-content">
          <p class="project-description">{{ description }}</p>

          <div class="project-details">
            <div class="detail-item">
              <span class="detail-label">项目类型：</span>
              <span class="detail-value">审计实训项目</span>
            </div>
            <div class="detail-item">
              <span class="detail-label">项目状态：</span>
              <span class="detail-value">进行中</span>
            </div>
            <div class="detail-item">
              <span class="detail-label">创建时间：</span>
              <span class="detail-value">{{ formatDate(new Date()) }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="action-section">
        <el-button
          type="primary"
          icon="el-icon-video-play"
          size="medium"
          @click="goToVideo"
          class="action-btn">
          观看项目视频
        </el-button>
      </div>
    </div>

    <!-- 右侧项目截图展示区域 -->
    <div class="right-panel">
      <div class="screenshot-header">
        <h3>
          <i class="el-icon-picture"></i>
          项目截图展示
        </h3>
      </div>

      <div class="screenshot-container">
        <div class="main-screenshot" v-if="images && images.length > 0 && images[0].src">
          <img :src="images[0].src" :alt="images[0].alt || '项目截图'" class="screenshot-image" />
          <div class="screenshot-overlay">
            <el-button
              type="primary"
              icon="el-icon-zoom-in"
              circle
              size="medium"
              @click="previewImage(images[0].src)"
              class="preview-btn">
            </el-button>
          </div>
        </div>
        <div class="no-screenshot" v-else>
          <i class="el-icon-picture-outline"></i>
          <p>暂无项目截图</p>
        </div>
      </div>
    </div>

    <!-- 图片预览对话框 -->
    <el-dialog
      :visible.sync="imagePreviewVisible"
      width="80%"
      center
      :show-close="true"
      class="image-preview-dialog">
      <img :src="previewImageSrc" style="width: 100%; height: auto;" />
    </el-dialog>
  </div>
</template>

<script>
import { getIntroductionjob  } from "@/api/system/introductionjob";

export default {
  //接收参数
  props: ['id'],
  data() {
    return {
      projectName: '',
      description: '',
      imageSrc: '',
      id:'',
      images: [
        { src: '', alt: '' },
      ],
      imagePreviewVisible: false,
      previewImageSrc: ''
    };
  },
  created() {
    var obj=this.$route.query;
    this.getDetail(obj['id']);
  },
  methods: {
    getDetail(intrId) {
      getIntroductionjob(intrId).then(response => {
        this.projectName = response.data.intrRemark;
        this.description = response.data.intrContent;
        this.images[0].src = process.env.VUE_APP_BASE_API + response.data.imageData;
        this.images[0].alt = this.projectName + ' 项目截图';
      });
    },
    goToVideo() {
      this.$router.push({
        path: '/video-player',
        query: {
          id: this.$route.query.id,
          title: this.projectName
        }
      });
    },
    previewImage(src) {
      this.previewImageSrc = src;
      this.imagePreviewVisible = true;
    },
    formatDate(date) {
      return date.toLocaleDateString('zh-CN');
    }
  }
};
</script>

<style scoped>
.audit-detail-container {
  display: flex;
  min-height: 100vh;
  background: #f5f7fa;
  font-family: 'Microsoft YaHei', Arial, sans-serif;
}

/* 左侧面板样式 */
.left-panel {
  width: 400px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 40px 30px;
  display: flex;
  flex-direction: column;
  position: relative;
  box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
}

.project-header {
  text-align: center;
  margin-bottom: 40px;
}

.project-icon {
  width: 80px;
  height: 80px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 20px;
  backdrop-filter: blur(10px);
}

.project-icon i {
  font-size: 36px;
  color: white;
}

.project-title {
  font-size: 28px;
  font-weight: 600;
  margin: 0 0 15px 0;
  line-height: 1.3;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.project-tags {
  display: flex;
  justify-content: center;
  gap: 10px;
  flex-wrap: wrap;
}

.project-info-section {
  flex: 1;
  margin-bottom: 30px;
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 2px solid rgba(255, 255, 255, 0.3);
  display: flex;
  align-items: center;
  gap: 8px;
}

.section-title i {
  font-size: 20px;
}

.info-content {
  background: rgba(255, 255, 255, 0.1);
  padding: 25px;
  border-radius: 12px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.project-description {
  font-size: 16px;
  line-height: 1.6;
  margin-bottom: 20px;
  color: rgba(255, 255, 255, 0.95);
}

.project-details {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.detail-item {
  display: flex;
  align-items: center;
  font-size: 14px;
}

.detail-label {
  font-weight: 600;
  min-width: 80px;
  color: rgba(255, 255, 255, 0.8);
}

.detail-value {
  color: white;
  font-weight: 500;
}

.action-section {
  text-align: center;
}

.action-btn {
  background: rgba(255, 255, 255, 0.2);
  border: 2px solid rgba(255, 255, 255, 0.3);
  color: white;
  padding: 12px 30px;
  font-size: 16px;
  font-weight: 600;
  border-radius: 25px;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.action-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
}

/* 右侧面板样式 */
.right-panel {
  flex: 1;
  padding: 40px;
  background: white;
}

.screenshot-header {
  margin-bottom: 30px;
}

.screenshot-header h3 {
  font-size: 24px;
  color: #2c3e50;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 10px;
  font-weight: 600;
}

.screenshot-header i {
  font-size: 28px;
  color: #667eea;
}

.screenshot-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 500px;
}

.main-screenshot {
  position: relative;
  max-width: 100%;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
  transition: transform 0.3s ease;
}

.main-screenshot:hover {
  transform: translateY(-5px);
}

.screenshot-image {
  width: 100%;
  height: auto;
  display: block;
  max-height: 600px;
  object-fit: contain;
}

.screenshot-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.main-screenshot:hover .screenshot-overlay {
  opacity: 1;
}

.preview-btn {
  background: rgba(255, 255, 255, 0.9);
  border: none;
  color: #667eea;
  font-size: 20px;
}

.no-screenshot {
  text-align: center;
  color: #999;
  font-size: 18px;
}

.no-screenshot i {
  font-size: 80px;
  color: #ddd;
  margin-bottom: 20px;
  display: block;
}

/* 图片预览对话框样式 */
.image-preview-dialog {
  text-align: center;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .audit-detail-container {
    flex-direction: column;
  }

  .left-panel {
    width: 100%;
    min-height: auto;
  }

  .right-panel {
    padding: 20px;
  }
}

@media (max-width: 768px) {
  .left-panel {
    padding: 20px;
  }

  .project-title {
    font-size: 24px;
  }

  .screenshot-header h3 {
    font-size: 20px;
  }
}
</style>
