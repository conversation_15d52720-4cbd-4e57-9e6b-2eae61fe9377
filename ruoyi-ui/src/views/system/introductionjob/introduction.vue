<!-- 备份版本：原始简洁设计 -->
<!--
<template>
  <div class="project-intro">
    <div class="header-section">
      <h1>{{ projectName }}</h1>
      <el-button
        type="primary"
        icon="el-icon-video-play"
        size="small"
        @click="goToVideo"
        class="video-btn">
        观看视频
      </el-button>
    </div>
    <p class="description">{{ description }}</p>

    <h2>项目截图</h2>
    <ImageGallery :images="images" />

  </div>
</template>
-->

<!-- 新版本：左右分栏美化设计 -->
<template>
  <div class="audit-detail-page">
    <div class="content-wrapper">
      <!-- 左侧信息面板 -->
      <div class="left-panel">
        <div class="panel-header">
          <div class="project-icon">
            <i class="el-icon-folder-opened"></i>
          </div>
          <h1 class="project-title">{{ projectName }}</h1>
          <div class="project-status">
            <el-tag type="success" size="small">进行中</el-tag>
          </div>
        </div>

        <div class="info-section">
          <h3 class="section-title">
            <i class="el-icon-info"></i>
            项目及项目组情况
          </h3>
          <div class="info-card">
            <div class="description-text">{{ description }}</div>

            <div class="project-meta">
              <div class="meta-item">
                <span class="meta-label">项目类型</span>
                <span class="meta-value">审计实训项目</span>
              </div>
              <div class="meta-item">
                <span class="meta-label">项目状态</span>
                <span class="meta-value">进行中</span>
              </div>
              <div class="meta-item">
                <span class="meta-label">更新时间</span>
                <span class="meta-value">{{ getCurrentDate() }}</span>
              </div>
            </div>
          </div>
        </div>

        <div class="action-area">
          <el-button
            type="primary"
            icon="el-icon-video-play"
            size="medium"
            @click="goToVideo"
            class="video-button">
            观看项目视频
          </el-button>
        </div>
      </div>

      <!-- 右侧截图展示面板 -->
      <div class="right-panel">
        <div class="screenshot-section">
          <h3 class="section-title">
            <i class="el-icon-picture-outline"></i>
            项目截图展示
          </h3>

          <div class="screenshot-display">
            <div class="image-container" v-if="images && images.length > 0 && images[0].src">
              <img
                :src="images[0].src"
                :alt="images[0].alt || '项目截图'"
                class="project-screenshot"
                @click="openImagePreview(images[0].src)"
              />
              <div class="image-overlay">
                <el-button
                  type="primary"
                  icon="el-icon-zoom-in"
                  circle
                  size="small"
                  @click="openImagePreview(images[0].src)">
                </el-button>
              </div>
            </div>
            <div class="no-image" v-else>
              <i class="el-icon-picture-outline-round"></i>
              <p>暂无项目截图</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 图片预览弹窗 -->
    <el-dialog
      :visible.sync="previewVisible"
      width="70%"
      center
      :show-close="true"
      append-to-body>
      <img :src="previewImageUrl" style="width: 100%; height: auto;" />
    </el-dialog>
  </div>
</template>

<script>
import { getIntroductionjob  } from "@/api/system/introductionjob";

export default {
  //接收参数
  props: ['id'],
  data() {
    return {
      projectName: '',
      description: '',
      imageSrc: '',
      id:'',
      images: [
        { src: '', alt: '' },
      ],
      previewVisible: false,
      previewImageUrl: ''
    };
  },
  created() {
    var obj=this.$route.query;
    this.getDetail(obj['id']);
  },
  methods: {
    getDetail(intrId) {
      getIntroductionjob(intrId).then(response => {
        this.projectName = response.data.intrRemark;
        this.description = response.data.intrContent;
        this.images[0].src = process.env.VUE_APP_BASE_API + response.data.imageData;
        this.images[0].alt = this.projectName + ' 项目截图';
      });
    },
    goToVideo() {
      this.$router.push({
        path: '/video-player',
        query: {
          id: this.$route.query.id,
          title: this.projectName
        }
      });
    },
    openImagePreview(imageUrl) {
      this.previewImageUrl = imageUrl;
      this.previewVisible = true;
    },
    getCurrentDate() {
      return new Date().toLocaleDateString('zh-CN');
    }
  }
};
</script>

<!-- 备份的原始样式 -->
<!--
<style scoped>
.project-intro {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
  font-family: Arial, sans-serif;
}

.header-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

h1 {
  color: #2c3e50;
  margin: 0;
  flex: 1;
  text-align: center;
}

.video-btn {
  margin-left: 20px;
}

.description {
  text-align: center;
  color: #666;
  font-size: 1.1em;
}

.tech-stack, .features, .links {
  margin-top: 30px;
}

h2 {
  color: #34495e;
  border-bottom: 2px solid #42b983;
  padding-bottom: 5px;
}

ul {
  list-style-type: none;
  padding: 0;
}

li {
  background: #f9f9f9;
  margin: 5px 0;
  padding: 10px;
  border-radius: 5px;
  color: #333;
}

.links {
  text-align: center;
}

.link {
  display: inline-block;
  margin: 10px;
  padding: 10px 20px;
  background: #42b983;
  color: white;
  text-decoration: none;
  border-radius: 5px;
}

.link:hover {
  background: #3aa876;
}
</style>
-->

<!-- 新版本美化样式 -->
<style scoped>
.audit-detail-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 20px;
  font-family: 'Microsoft YaHei', 'Helvetica Neue', Arial, sans-serif;
}

.content-wrapper {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  gap: 30px;
  min-height: calc(100vh - 40px);
}

/* 左侧面板 */
.left-panel {
  width: 400px;
  background: #fff;
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  padding: 30px;
  display: flex;
  flex-direction: column;
  position: relative;
  overflow: hidden;
}

.left-panel::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
}

.panel-header {
  text-align: center;
  margin-bottom: 30px;
}

.project-icon {
  width: 70px;
  height: 70px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 20px;
  box-shadow: 0 4px 20px rgba(102, 126, 234, 0.3);
}

.project-icon i {
  font-size: 32px;
  color: white;
}

.project-title {
  font-size: 24px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 15px 0;
  line-height: 1.3;
}

.project-status {
  margin-bottom: 10px;
}

.info-section {
  flex: 1;
  margin-bottom: 30px;
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  color: #34495e;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  gap: 8px;
  padding-bottom: 10px;
  border-bottom: 2px solid #e9ecef;
}

.section-title i {
  font-size: 20px;
  color: #667eea;
}

.info-card {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 25px;
  border: 1px solid #e9ecef;
}

.description-text {
  font-size: 15px;
  line-height: 1.6;
  color: #555;
  margin-bottom: 20px;
}

.project-meta {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.meta-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #e9ecef;
}

.meta-item:last-child {
  border-bottom: none;
}

.meta-label {
  font-weight: 500;
  color: #6c757d;
  font-size: 14px;
}

.meta-value {
  font-weight: 600;
  color: #2c3e50;
  font-size: 14px;
}

.action-area {
  text-align: center;
}

.video-button {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  padding: 12px 30px;
  font-size: 16px;
  font-weight: 600;
  border-radius: 25px;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
  transition: all 0.3s ease;
}

.video-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

/* 右侧面板 */
.right-panel {
  flex: 1;
  background: #fff;
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  padding: 30px;
  position: relative;
  overflow: hidden;
}

.right-panel::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
}

.screenshot-section {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.screenshot-display {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 400px;
}

.image-container {
  position: relative;
  max-width: 100%;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  transition: transform 0.3s ease;
  cursor: pointer;
}

.image-container:hover {
  transform: translateY(-5px);
}

.project-screenshot {
  width: 100%;
  height: auto;
  display: block;
  max-height: 500px;
  object-fit: contain;
}

.image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.image-container:hover .image-overlay {
  opacity: 1;
}

.no-image {
  text-align: center;
  color: #adb5bd;
  font-size: 16px;
}

.no-image i {
  font-size: 80px;
  color: #dee2e6;
  margin-bottom: 20px;
  display: block;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .content-wrapper {
    flex-direction: column;
    gap: 20px;
  }

  .left-panel {
    width: 100%;
  }
}

@media (max-width: 768px) {
  .audit-detail-page {
    padding: 10px;
  }

  .left-panel,
  .right-panel {
    padding: 20px;
  }

  .project-title {
    font-size: 20px;
  }

  .section-title {
    font-size: 16px;
  }
}
</style>
