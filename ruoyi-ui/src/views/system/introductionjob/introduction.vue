<template>
  <div class="project-intro">
    <div class="header-section">
      <h1>{{ projectName }}</h1>
      <el-button
        type="primary"
        icon="el-icon-video-play"
        size="small"
        @click="goToVideo"
        class="video-btn">
        观看视频
      </el-button>
    </div>
    <p class="description">{{ description }}</p>

    <h2>项目截图</h2>
    <ImageGallery :images="images" />

  </div>
</template>

<script>
import { getIntroductionjob  } from "@/api/system/introductionjob";
import ImageGallery from './ImageGallery.vue';
export default {
  //接收参数
  props: ['id'],
  components: {
    ImageGallery
  },
  data() {
    return {
      projectName: '',
      description: '',
      techStack: ['Vue.js', 'Vuex', 'Vue Router', 'Axios', 'Element UI'],
      features: [
        '用户登录/注册',
        '数据可视化',
        '权限管理',
        '响应式布局',
        'RESTful API 集成'
      ],
      imageSrc: '',
      id:'',
      images: [
              { src: '', alt: '' },
      ],
      githubLink: 'https://github.com/your-repo',
      demoLink: 'https://your-demo-site.com'
    };
  },
  created() {
  var obj=this.$route.query;
   this.getDetail(obj['id']);
  },
  methods: {
    getDetail(intrId) {
      getIntroductionjob(intrId).then(response => {
          this.projectName = response.data.intrRemark;
          this.description=response.data.intrContent;
          this.images[0].src= process.env.VUE_APP_BASE_API + response.data.imageData;
       });
    },
    goToVideo() {
      this.$router.push({
        path: '/video-player',
        query: {
          id: this.$route.query.id,
          title: this.projectName
        }
      });
    }
  }
};
</script>

<style scoped>
.project-intro {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
  font-family: Arial, sans-serif;
}

.header-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

h1 {
  color: #2c3e50;
  margin: 0;
  flex: 1;
  text-align: center;
}

.video-btn {
  margin-left: 20px;
}

.description {
  text-align: center;
  color: #666;
  font-size: 1.1em;
}

.tech-stack, .features, .links {
  margin-top: 30px;
}

h2 {
  color: #34495e;
  border-bottom: 2px solid #42b983;
  padding-bottom: 5px;
}

ul {
  list-style-type: none;
  padding: 0;
}

li {
  background: #f9f9f9;
  margin: 5px 0;
  padding: 10px;
  border-radius: 5px;
  color: #333;
}

.links {
  text-align: center;
}

.link {
  display: inline-block;
  margin: 10px;
  padding: 10px 20px;
  background: #42b983;
  color: white;
  text-decoration: none;
  border-radius: 5px;
}

.link:hover {
  background: #3aa876;
}
</style>
