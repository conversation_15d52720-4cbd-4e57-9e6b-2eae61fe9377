<template>
  <div class="professional-layout">
    <div class="layout-container">
      <!-- 左侧信息栏 -->
      <div class="info-sidebar">
        <div class="project-header">
          <div class="project-icon">
            <i class="el-icon-office-building"></i>
          </div>
          <h1 class="project-name">{{ projectName }}</h1>
        </div>

        <div class="info-section">
          <h3><i class="el-icon-document-copy"></i> 项目及项目组情况</h3>
          <p class="description">{{ description }}</p>
        </div>

        <div class="action-section">
          <el-button
            @click="goToVideo"
            class="action-btn">
            <i class="el-icon-video-play"></i> 观看介绍视频
          </el-button>
        </div>
      </div>

      <!-- 右侧内容区 -->
      <div class="content-main">
        <div class="section-header">
          <h2><i class="el-icon-picture-outline"></i> 项目截图展示</h2>
          <div class="section-divider"></div>
        </div>

        <div class="main-screenshot-container">
          <div class="screenshot-display">
            <div class="screenshot-content" v-if="images && images.length > 0 && images[0].src">
              <img
                :src="images[0].src"
                :alt="images[0].alt || '项目截图'"
                class="project-screenshot"
              />
            </div>
            <div class="screenshot-placeholder" v-else>
              <div class="placeholder-content">
                <i class="el-icon-picture-outline"></i>
                <h3>项目截图展示</h3>
                <p>暂无项目截图</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getIntroductionjob  } from "@/api/system/introductionjob";

export default {
  //接收参数
  props: ['id'],
  data() {
    return {
      projectName: '',
      description: '',
      imageSrc: '',
      id:'',
      images: [
        { src: '', alt: '' },
      ]
    };
  },
  created() {
    var obj=this.$route.query;
    this.getDetail(obj['id']);
  },
  methods: {
    getDetail(intrId) {
      getIntroductionjob(intrId).then(response => {
        this.projectName = response.data.intrRemark;
        this.description = response.data.intrContent;
        this.images[0].src = process.env.VUE_APP_BASE_API + response.data.imageData;
        this.images[0].alt = this.projectName + ' 项目截图';
      });
    },
    goToVideo() {
      this.$router.push({
        path: '/video-player',
        query: {
          id: this.$route.query.id,
          title: this.projectName
        }
      });
    }
  }
};
</script>

<style scoped>
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

.professional-layout {
  min-height: 100vh;
  background: linear-gradient(to right, #e3f2fd 0%, #f8f9fa 50%, #fff 100%);
  font-family: 'Microsoft YaHei', 'Helvetica Neue', Arial, sans-serif;
  padding: 0;
  margin: 0;
}

.layout-container {
  display: flex;
  min-height: 100vh;
  margin: 0;
  padding: 0;
}

/* 左侧信息栏样式 - 使用参考HTML的颜色 */
.info-sidebar {
  width: 400px;
  background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
  color: white;
  padding: 40px 30px;
  position: relative;
  margin-left: 0;
}

.info-sidebar::before {
  content: '';
  position: absolute;
  top: 0;
  right: -20px;
  width: 40px;
  height: 100%;
  background: linear-gradient(135deg, #2a5298 0%, transparent 100%);
  clip-path: polygon(0 0, 50% 0, 0 100%);
}

.project-header {
  text-align: center;
  margin-bottom: 40px;
}

.project-icon {
  width: 80px;
  height: 80px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 20px;
  backdrop-filter: blur(10px);
}

.project-icon i {
  font-size: 36px;
  color: white;
}

.project-name {
  font-size: 1.8rem;
  font-weight: 700;
  margin-bottom: 20px;
  line-height: 1.3;
}

.info-section {
  margin-bottom: 40px;
}

.info-section h3 {
  font-size: 1.2rem;
  margin-bottom: 15px;
  display: flex;
  align-items: center;
  border-bottom: 2px solid rgba(255, 255, 255, 0.3);
  padding-bottom: 10px;
}

.info-section h3 i {
  margin-right: 10px;
  font-size: 18px;
}

.description {
  line-height: 1.8;
  font-size: 14px;
  opacity: 0.9;
  text-align: justify;
}

.action-section {
  text-align: center;
}

.action-btn {
  width: 100%;
  background: rgba(255, 255, 255, 0.2) !important;
  border: 2px solid rgba(255, 255, 255, 0.3) !important;
  color: white !important;
  padding: 15px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.action-btn:hover {
  background: rgba(255, 255, 255, 0.3) !important;
  transform: translateY(-2px);
}

/* 右侧内容区样式 */
.content-main {
  flex: 1;
  padding: 40px;
  background: white;
  margin-left: 0;
}

.section-header {
  margin-bottom: 30px;
}

.section-header h2 {
  font-size: 1.8rem;
  color: #1e3c72;
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.section-header h2 i {
  margin-right: 12px;
  font-size: 24px;
}

.section-divider {
  height: 4px;
  background: linear-gradient(to right, #1e3c72, #2a5298, transparent);
  border-radius: 2px;
  width: 100px;
}

.main-screenshot-container {
  width: 100%;
  margin-top: 20px;
}

.screenshot-display {
  background: white;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  border: 1px solid #e9ecef;
  min-height: 400px;
}

.screenshot-display:hover {
  transform: translateY(-3px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

/* 实际截图样式 */
.screenshot-content {
  height: 500px;
  overflow: hidden;
}

.project-screenshot {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
}

/* 占位符样式 */
.screenshot-placeholder {
  height: 500px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

.placeholder-content {
  text-align: center;
  color: white;
  z-index: 2;
  position: relative;
}

.placeholder-content i {
  font-size: 64px;
  margin-bottom: 20px;
  opacity: 0.9;
}

.placeholder-content h3 {
  font-size: 1.8rem;
  margin-bottom: 12px;
  font-weight: 600;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.placeholder-content p {
  font-size: 1rem;
  opacity: 0.8;
  margin: 0;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .layout-container {
    flex-direction: column;
  }

  .info-sidebar {
    width: 100%;
    padding: 30px 20px;
  }

  .info-sidebar::before {
    display: none;
  }

  .content-main {
    padding: 30px 20px;
  }
}

@media (max-width: 768px) {
  .screenshot-placeholder,
  .screenshot-content {
    height: 300px;
  }

  .placeholder-content i {
    font-size: 48px;
    margin-bottom: 15px;
  }

  .placeholder-content h3 {
    font-size: 1.4rem;
    margin-bottom: 10px;
  }

  .placeholder-content p {
    font-size: 0.9rem;
  }

  .project-name {
    font-size: 1.5rem;
  }
}

@media (max-width: 480px) {
  .screenshot-placeholder,
  .screenshot-content {
    height: 250px;
  }

  .placeholder-content i {
    font-size: 40px;
    margin-bottom: 12px;
  }

  .placeholder-content h3 {
    font-size: 1.2rem;
    margin-bottom: 8px;
  }

  .placeholder-content p {
    font-size: 0.8rem;
  }
}
</style>
