<template>
  <div class="video-player-container">
    <div class="video-header">
      <h1>{{ title || '视频播放' }}</h1>
      <el-button 
        type="primary" 
        icon="el-icon-back" 
        size="small"
        @click="goBack"
        class="back-btn">
        返回
      </el-button>
    </div>
    
    <div class="video-content">
      <div class="video-wrapper">
        <video 
          ref="videoPlayer"
          :src="videoUrl"
          controls
          width="100%"
          height="500"
          @loadstart="onVideoLoadStart"
          @error="onVideoError">
          您的浏览器不支持视频播放
        </video>
      </div>
      
      <div class="video-info" v-if="videoInfo">
        <h3>视频信息</h3>
        <p><strong>标题:</strong> {{ videoInfo.title }}</p>
        <p><strong>描述:</strong> {{ videoInfo.description }}</p>
        <p><strong>时长:</strong> {{ formatDuration(videoInfo.duration) }}</p>
      </div>
      
      <!-- 暂时显示提示信息，因为还没有实际的视频源 -->
      <div class="placeholder" v-if="!videoUrl">
        <el-alert
          title="视频功能开发中"
          type="info"
          description="视频播放功能正在开发中，请稍后再试。"
          show-icon
          :closable="false">
        </el-alert>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'VideoPlayer',
  data() {
    return {
      title: '',
      videoUrl: '', // 这里将来需要根据项目ID获取对应的视频URL
      videoInfo: null,
      projectId: null
    }
  },
  created() {
    this.projectId = this.$route.query.id;
    this.title = this.$route.query.title || '视频播放';
    
    // 这里将来可以根据projectId获取对应的视频信息
    this.loadVideoInfo();
  },
  methods: {
    goBack() {
      this.$router.go(-1);
    },
    
    loadVideoInfo() {
      // 这里将来需要调用API获取视频信息
      // 暂时使用模拟数据
      if (this.projectId) {
        this.videoInfo = {
          title: this.title,
          description: '项目介绍视频',
          duration: 300 // 5分钟，以秒为单位
        };
        
        // 这里将来需要根据projectId获取真实的视频URL
        // this.videoUrl = `/api/video/${this.projectId}`;
      }
    },
    
    formatDuration(seconds) {
      if (!seconds) return '未知';
      const minutes = Math.floor(seconds / 60);
      const secs = seconds % 60;
      return `${minutes}:${secs.toString().padStart(2, '0')}`;
    },
    
    onVideoLoadStart() {
      console.log('视频开始加载');
    },
    
    onVideoError(error) {
      console.error('视频加载失败:', error);
      this.$message.error('视频加载失败，请稍后再试');
    }
  }
}
</script>

<style scoped>
.video-player-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.video-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid #eee;
}

.video-header h1 {
  color: #2c3e50;
  margin: 0;
}

.back-btn {
  margin-left: 20px;
}

.video-content {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  overflow: hidden;
}

.video-wrapper {
  position: relative;
  background: #000;
}

.video-wrapper video {
  display: block;
  width: 100%;
  height: 500px;
  object-fit: contain;
}

.video-info {
  padding: 20px;
  background: #f9f9f9;
}

.video-info h3 {
  color: #2c3e50;
  margin-bottom: 15px;
}

.video-info p {
  margin: 8px 0;
  color: #666;
}

.placeholder {
  padding: 40px;
  text-align: center;
}

@media (max-width: 768px) {
  .video-player-container {
    padding: 10px;
  }
  
  .video-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
  
  .video-wrapper video {
    height: 250px;
  }
}
</style>