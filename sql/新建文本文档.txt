CREATE TABLE `course` (
  `course_id` bigint NOT NULL AUTO_INCREMENT COMMENT '课程ID',
  `title` varchar(255) NOT NULL COMMENT '课程标题',
  `subtitle` varchar(255) DEFAULT NULL COMMENT '课程副标题',
  `teacher` varchar(50) DEFAULT NULL COMMENT '讲师',
  `teacher_avatar` varchar(2550) DEFAULT NULL COMMENT '讲师头像',
  `date` varchar(20) DEFAULT NULL COMMENT '上课日期',
  `students` int DEFAULT '0' COMMENT '学生人数',
  `status` varchar(20) DEFAULT NULL COMMENT '状态',
  `category_id` bigint DEFAULT NULL COMMENT '所属分类ID',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `draft` text COMMENT '课程底稿',
  `create_by` varchar(64) DEFAULT NULL,
  `create_time` datetime DEFAULT NULL,
  `update_by` varchar(64) DEFAULT NULL,
  `update_time` datetime DEFAULT NULL,
  PRIMARY KEY (`course_id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='课程表';


CREATE TABLE `sys_file_category` (
  `category_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '分类ID',
  `parent_id` bigint(20) DEFAULT '0' COMMENT '父分类ID',
  `ancestors` varchar(50) DEFAULT '' COMMENT '祖级列表',
  `category_name` varchar(30) NOT NULL COMMENT '分类名称',
  `order_num` int(4) DEFAULT '0' COMMENT '显示顺序',
  `status` char(1) DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `del_flag` char(1) DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`category_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='文件分类表';

CREATE TABLE `sys_file_info` (
  `file_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '文件ID',
  `category_id` bigint(20) DEFAULT NULL COMMENT '分类ID',
  `file_name` varchar(100) NOT NULL COMMENT '文件名称',
  `file_path` varchar(255) NOT NULL COMMENT '文件路径',
  `file_url` varchar(255) NOT NULL COMMENT '文件URL',
  `file_size` bigint(20) DEFAULT '0' COMMENT '文件大小(字节)',
  `file_type` varchar(50) DEFAULT '' COMMENT '文件类型',
  `status` char(1) DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `del_flag` char(1) DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`file_id`),
  KEY `idx_category_id` (`category_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='文件信息表';
