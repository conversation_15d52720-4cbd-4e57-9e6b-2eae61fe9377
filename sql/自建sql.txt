

CREATE TABLE `IntroductionJob` (
  `Intr_id` bigint NOT NULL AUTO_INCREMENT COMMENT '项目介绍ID',
  `menu_id` bigint  COMMENT '项目介绍条目对应的菜单id，因为这个介绍要在菜单中显示',
  `Intr_content` varchar(3000) NOT NULL COMMENT '介绍内容',
   image_data varchar(3000)  COMMENT '存储图片数据',
   `Intr_remark` varchar(3000)  COMMENT '备注',
  PRIMARY KEY (`Intr_id`)
) ENGINE=InnoDB AUTO_INCREMENT=101 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='项目介绍信息表';


CREATE TABLE chathistory(
  chat_id bigint NOT NULL AUTO_INCREMENT COMMENT '聊天ID',
   user_id bigint  COMMENT '用户id',
   chat_tile varchar(3000)  COMMENT '聊天标题',
    chat_content varchar(3000)  COMMENT '聊天内容',
    chat_time  varchar(100)  COMMENT '记录时间',
    `chat_remark` varchar(3000)  COMMENT '备注',
    PRIMARY KEY (`chat_id`)
)ENGINE=InnoDB AUTO_INCREMENT=101 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='聊天记录';



CREATE TABLE bill(
  bill_id bigint NOT NULL AUTO_INCREMENT COMMENT '账单ID',
   bill_date varchar(100)  COMMENT '账单日期',
   voucher_no varchar(100)  COMMENT '凭证号',
   bill_direct varchar(100)  COMMENT '账单方向',
   firstlevel varchar(100)  COMMENT '一级科目',
   secondlevel varchar(100)  COMMENT '二级科目',
   thirdlevel varchar(100)  COMMENT '三级科目',
   accountproject varchar(100)  COMMENT '核算项目',	
   occurretamount varchar(100)  COMMENT '发生额',	
   balance_direct varchar(100)  COMMENT '余额方向',
   balance varchar(100)  COMMENT '余额',
   notes varchar(1000)  COMMENT '备注',
   bak1 varchar(1000)  COMMENT '备用1',
   bak2 varchar(1000)  COMMENT '备用2',
   bak3 varchar(1000)  COMMENT '备用3',
   bak4 varchar(1000)  COMMENT '备用4',
   bak5 varchar(1000)  COMMENT '备用5',
   doc1 varchar(1000)  COMMENT '单据1',
   doc2 varchar(1000)  COMMENT '单据2',
   doc3 varchar(1000)  COMMENT '单据3',
   doc4 varchar(1000)  COMMENT '单据4',
   doc5 varchar(1000)  COMMENT '单据5',
    PRIMARY KEY (`bill_id`)
)ENGINE=InnoDB AUTO_INCREMENT=101 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='账单';

CREATE TABLE excel_data (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255),
    value DOUBLE,
    formula VARCHAR(255)
);

CREATE TABLE excel_info (
    info_id BIGINT AUTO_INCREMENT PRIMARY KEY,
    excel_name VARCHAR(255) COMMENT '课件名称',
    excel_path VARCHAR(255) COMMENT '路径',
    excel_remark VARCHAR(255)  COMMENT '备注'
);
CREATE TABLE userexcel_info (
    userexcel_id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT COMMENT '用户ID',
    excel_id BIGINT COMMENT 'ExcelID',
    excelDoc_date VARCHAR(255)  COMMENT '文档日期',
    excelDoc_content VARCHAR(4860) COMMENT '数据内容',
    excelDoc_remark VARCHAR(255)  COMMENT '备注'
);
