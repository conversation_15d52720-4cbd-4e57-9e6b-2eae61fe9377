#!/bin/bash

echo "🐳 启动审计实训教学系统容器环境"
echo "=================================="

# 检查Docker是否运行
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker未运行，请先启动Docker"
    exit 1
fi

# 检查OrbStack是否运行
if ! command -v docker > /dev/null 2>&1; then
    echo "❌ Docker命令不可用，请检查OrbStack安装"
    exit 1
fi

echo "✅ Docker环境检查通过"

# 启动MySQL和Redis服务
echo "🚀 启动MySQL和Redis容器..."
docker-compose up -d mysql redis

# 等待MySQL启动
echo "⏳ 等待MySQL初始化..."
sleep 30

# 检查MySQL是否启动成功
if docker exec audit-mysql mysqladmin ping -h localhost -u root -p123456 --silent; then
    echo "✅ MySQL启动成功"
else
    echo "❌ MySQL启动失败，请检查日志："
    docker-compose logs mysql
    exit 1
fi

# 检查Redis是否启动成功
if docker exec audit-redis redis-cli ping > /dev/null 2>&1; then
    echo "✅ Redis启动成功"
else
    echo "❌ Redis启动失败，请检查日志："
    docker-compose logs redis
    exit 1
fi

# 显示容器状态
echo ""
echo "📊 容器状态："
docker-compose ps

echo ""
echo "🎉 容器环境启动完成！"
echo ""
echo "📝 接下来的步骤："
echo "1. 启动后端服务: mvn spring-boot:run"
echo "2. 启动前端服务: cd ruoyi-ui && npm run dev"
echo ""
echo "🔍 服务地址："
echo "  - MySQL:   localhost:3306"
echo "  - Redis:   localhost:6379"
echo "  - 后端API: http://localhost:8080"
echo "  - 前端:    http://localhost:80"
echo ""
echo "🛠 管理命令："
echo "  - 查看日志: docker-compose logs -f [service]"
echo "  - 停止服务: docker-compose down"
echo "  - 重启服务: docker-compose restart"