<template>
<div class="chat-app">
    <!-- 左侧树控件 -->
    <div class="left-side">
      <el-tree
        :data="treeData"
        :props="defaultProps"
        @node-click="download"
      ></el-tree>
      <div style="display: grid;">
           <el-button type="primary" size="mini" @click="handleSave">保存</el-button>
       </div>
    </div>

    <!-- 右侧聊天区域 -->
    <div class="right-side">
          <div ref="hotTable" class="handsontable-container"></div>
    </div>
  </div>
</template>

---

### ✅ 核心逻辑代码

```javascript
<script>
import Handsontable from "handsontable";
import "handsontable/dist/handsontable.full.css";
import ExcelJS from "exceljs";
import HyperFormula from "hyperformula";  // ✅ 公式计算引擎
import { listInfo,addExcelData,updateExcelData,excelist } from "@/api/system/info";
import request from '@/utils/request';
import axios from "axios";
import { getToken } from '@/utils/auth';

const baseURL = process.env.VUE_APP_BASE_API;

export default {
  data() {
    return {
      hotInstance: null,
      treeData: [],
      defaultProps: {
         children: 'children',
         label: 'label'
      },
      cellStyles: {},         // 样式
      mergeCells: [],          // 合并单元格信息
      hyperformula: null,      // 公式引擎
      serverFiles: [],         // 服务器文件
      selectedFile: "",        // 选中文件
      form: {},// 表单参数
      queryParams: {}
    };
  },

  created() {
    this.getList();
    this.makeTree();
    this.form = {
            userexcelId: null,
            userId: null,
            excelId: null,
            exceldocDate: null,
            exceldocContent: null,
            exceldocRemark: null
    };
  },

  methods: {
  makeTree() {
    excelist().then(response => {
        this.treeData = response; // 将获取的数据赋值给 treeData
     });
    },

    // ✅ 获取服务器文件列表
    getList() {
      listInfo(this.queryParams).then(response => {
        this.serverFiles = response.rows.map(row => ({
          fileId: row.infoId,
          fileName: row.excelName
        }));
      });
    },

    // ✅ 从服务器下载并显示 Excel
    async download(data) {
    const jsonString = JSON.stringify(data);
    const obj=JSON.parse(jsonString);
    this.selectedFile=JSON.parse(obj.id);
      if (!this.selectedFile) {
        alert("请选择文件");
        return;
      }
      try {
        const arrayBuffer = await this.downloadFile();
        await this.loadExcelData(arrayBuffer);
      } catch (error) {
        console.error("下载失败:", error);
        alert("下载失败");
      }
    },

    async handleSave() {
    try {
         // 弹出确认对话框
         await this.$modal.confirm('确定要保存数据吗？')
            // 用户点击确定后执行保存
         this.form.exceldocContent = JSON.stringify(this.hotInstance.getData());
         this.form.excelId = this.selectedFile;
         const date = new Date();
         const year = date.getFullYear();
         const month = String(date.getMonth() + 1).padStart(2, '0');
         const day = String(date.getDate()).padStart(2, '0');

         this.form.exceldocDate = `${year}-${month}-${day}`;
         //console.log('this.$store.getters.user.userId',this.$store.getters.user.userId);
         this.form.userId =this.$store.state.user.id;
         if(this.form.userexcelId == null)
         {
           await addExcelData(this.form);
         }
         else{
           await updateExcelData(this.form);
         }
         this.$modal.msgSuccess('保存成功')
      } catch (error) {
         // 用户点击取消会进入catch，error不是真正的错误
         if (error !== 'cancel') {
           console.error('保存失败:', error)
           Message.error('保存失败: ' + (error.message || error))
         }
       }
    },


    async downloadFile() {
      const url = `${baseURL}/system/excel/download?id=${this.selectedFile}`;
      try {
        const response = await axios.get(url, {
          responseType: 'arraybuffer',
          headers: { 'Authorization': `Bearer ${getToken()}` }
        });
        return response.data;
      } catch (error) {
        throw new Error("文件下载失败");
      }
    },

    // ✅ 本地上传文件处理
    async handleFileUpload(event) {
      const file = event.target.files[0];
      if (!file) return;

      const reader = new FileReader();
      reader.readAsArrayBuffer(file);
      reader.onload = async (e) => {
        const arrayBuffer = e.target.result;
        await this.loadExcelData(arrayBuffer);
      };
    },

    // ✅ 解析 Excel 数据
    async loadExcelData(arrayBuffer) {
      const workbook = new ExcelJS.Workbook();
      await workbook.xlsx.load(arrayBuffer);
      const worksheet = workbook.getWorksheet(1);

      // 提取数据、样式和合并信息
      const data = this.extractSheetData(worksheet);
      this.cellStyles = this.extractCellStyles(worksheet);
      this.mergeCells = this.extractMergeCells(worksheet);

      this.initHyperFormula();
      this.renderHandsontable(data);

      // ✅ 替换 #ERROR! 为 0.00
            this.$nextTick(() => {
              this.replaceErrorsWithDefault();
            });
    },

    // ✅ 替换 #ERROR! 为 0.00
        replaceErrorsWithDefault() {
          if (!this.hotInstance) return;

          const rowCount = this.hotInstance.countRows();
          const colCount = this.hotInstance.countCols();

          for (let row = 0; row < rowCount; row++) {
            for (let col = 0; col < colCount; col++) {
              const value = this.hotInstance.getDataAtCell(row, col);
              if (value === "#ERROR!") {
                this.hotInstance.setDataAtCell(row, col, "0.00");
              }
            }
          }
        },

    // ✅ 解析数据
    extractSheetData(worksheet) {
      const data = [];
      worksheet.eachRow({ includeEmpty: true }, (row) => {
        const rowData = [];
        row.eachCell({ includeEmpty: true }, (cell) => {
          let value = cell.value;

          // 处理公式和富文本
          if (cell.formula) {
            value = `=${cell.formula}`;
          } else if (value && typeof value === "object") {
            if (value.richText) {
              value = value.richText.map(rt => rt.text).join('');
            } else if (value.result !== undefined) {
              value = value.result;
            }
          }

          rowData.push(value || "");
        });
        data.push(rowData);
      });

      return data;
    },

    // ✅ 提取合并单元格信息
    extractMergeCells(worksheet) {
      const mergeCells = [];
      worksheet.model.merges.forEach((merge) => {
        const [start, end] = merge.split(":");
        const startCell = this.decodeCellAddress(start);
        const endCell = this.decodeCellAddress(end);

        mergeCells.push({
          row: startCell.row,
          col: startCell.col,
          rowspan: endCell.row - startCell.row + 1,
          colspan: endCell.col - startCell.col + 1
        });
      });
      return mergeCells;
    },

    // ✅ 提取单元格样式
    extractCellStyles(worksheet) {
      const styles = {};
      worksheet.eachRow({ includeEmpty: true }, (row, rowIndex) => {
        row.eachCell({ includeEmpty: true }, (cell, colIndex) => {
          const address = this.encodeCellAddress(rowIndex - 1, colIndex - 1);
          const style = cell.style;

          styles[address] = {
            backgroundColor: style.fill?.fgColor?.argb ? `#${style.fill.fgColor.argb.slice(2)}` : null,
            fontColor: style.font?.color?.argb ? `#${style.font.color.argb.slice(2)}` : null,
            bold: style.font?.bold || false,
            italic: style.font?.italic || false,
            fontSize: style.font?.size || 12,
            textAlign: style.alignment?.horizontal || "left",
            border: this.extractBorderStyle(style.border)
          };
        });
      });

      return styles;
    },

    // ✅ 提取边框样式
    extractBorderStyle(border) {
      if (!border) return null;
      let style = {};
      if (border.left) style.borderLeft = "1px solid #000";
      if (border.right) style.borderRight = "1px solid #000";
      if (border.top) style.borderTop = "1px solid #000";
      if (border.bottom) style.borderBottom = "1px solid #000";
      return style;
    },

    // ✅ 将 Excel 地址转换为 Handsontable 索引
    decodeCellAddress(address) {
      const match = address.match(/([A-Z]+)(\d+)/);
      if (!match) return { row: 0, col: 0 };

      const colLetters = match[1];
      const rowNumber = parseInt(match[2], 10) - 1;
      let colNumber = 0;

      for (let i = 0; i < colLetters.length; i++) {
        colNumber = colNumber * 26 + (colLetters.charCodeAt(i) - 64);
      }

      return { row: rowNumber, col: colNumber - 1 };
    },

    // ✅ 将行列索引转换为 Excel 地址
    encodeCellAddress(row, col) {
      let colLetters = "";
      col += 1;
      while (col > 0) {
        let remainder = (col - 1) % 26;
        colLetters = String.fromCharCode(65 + remainder) + colLetters;
        col = Math.floor((col - 1) / 26);
      }
      return `${colLetters}${row + 1}`;
    },

    // ✅ 初始化 HyperFormula
    initHyperFormula() {
      this.hyperformula = HyperFormula.buildEmpty({
        licenseKey: "internal-use-in-handsontable"
      });
    },

    // ✅ 渲染 Handsontable
    renderHandsontable(data) {
      if (this.hotInstance) {
        this.hotInstance.destroy();
      }

      this.hotInstance = new Handsontable(this.$refs.hotTable, {
        data,
        rowHeaders: true,
        colHeaders: true,
        mergeCells: this.mergeCells,
        formulas: { engine: this.hyperformula },
        cells: (row, col) => {
          return { renderer: this.customRenderer };
        }
      });
    },

    // ✅ 自定义渲染器
    customRenderer(instance, td, row, col, prop, value, cellProperties) {
          Handsontable.renderers.TextRenderer.apply(this, arguments);
          const address = this.encodeCellAddress(row, col);
          const style = this.cellStyles[address];

          if (style) {
            td.style.backgroundColor = style.backgroundColor || "";
            td.style.color = style.fontColor || "";
            td.style.fontWeight = style.bold ? "bold" : "";
            td.style.fontStyle = style.italic ? "italic" : "";
            td.style.textAlign = style.textAlign || "";
            Object.assign(td.style, style.border);
          }
        }
  }
};
</script>

<style>
.handsontable-container {
  width: 100%;
  height: 500px;
  overflow: auto;
}

.chat-app {
  display: flex;
    height: 100vh;
}

.left-side {
  width: 150px;
  border-right: 1px solid #ddd;
  padding: 10px;
  overflow-y: auto;
}

.right-side {
  flex: 1;
  display: flex;
  flex-direction: column;
}

</style>
<style scoped>
/* 图片查看器对话框样式 */
.image-viewer-dialog {
  display: flex;
  flex-direction: column;
}

/* 轮播容器样式 */
.full-image-carousel {
  flex: 1;
  display: flex;
  flex-direction: column;
}

/* 轮播项样式 */
.carousel-item-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f5f5f5;
  height: 100%;
}

/* 图片样式 */
.full-size-image {
  width: 100%;
  height: 100%;
  display: block;
}

.full-size-image .el-image__inner {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

/* 图片加载错误提示 */
.image-error {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
  color: #999;
}
</style>

