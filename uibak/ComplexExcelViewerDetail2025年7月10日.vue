<template>
<div class="chat-app">
    <!-- 左侧树控件 -->

    <!-- 右侧聊天区域 -->
    <div>
    <el-button type="primary" size="mini" @click="handleSave">保存</el-button>
     <el-button type="success" size="mini" @click="exportToExcel">导出 Excel</el-button>  <!-- ✅ 新增导出按钮 -->
     <div ref="hotTable" class="handsontable-container"></div>
    </div>
  </div>
</template>

---

### ✅ 核心逻辑代码

```javascript
<script>
import Handsontable from "handsontable";
import "handsontable/dist/handsontable.full.css";
import ExcelJS from "exceljs";
import HyperFormula from "hyperformula";  // ✅ 公式计算引擎
import { listInfo,addExcelData,updateExcelData,excelist,getExcelInfo } from "@/api/system/info";
import request from '@/utils/request';
import axios from "axios";
import { getToken } from '@/utils/auth';

const baseURL = process.env.VUE_APP_BASE_API;

export default {
 props: {
    id: {
      type: [String, Number],
      required: true
    }
  },
  data() {
    return {
      hotInstance: null,
      treeData: [],
      defaultProps: {
         children: 'children',
         label: 'label'
      },
      cellStyles: {},         // 样式
      mergeCells: [],          // 合并单元格信息
      hyperformula: null,      // 公式引擎
      serverFiles: [],         // 服务器文件
      selectedFile: "",        // 选中文件
      form: {},// 表单参数
      queryParams: {}
    };
  },

  async mounted() {
      console.log("组件挂载中...");
      this.form = {
        userexcelId: null,
        userId: null,
        excelId: null,
        exceldocDate: null,
        exceldocContent: null,
        exceldocRemark: null
      };
      this.selectedFile = this.id;
      try {
        await this.getExcel();
      } catch (error) {
        console.error("加载失败:", error);
        this.$modal.msgError("加载数据失败");
      }
    },

  methods: {

  // ✅ 导出 Excel 方法
      async exportToExcel() {
        if (!this.hotInstance) {
          this.$modal.msgError("没有可导出的数据！");
          return;
        }

        const workbook = new ExcelJS.Workbook();
        const worksheet = workbook.addWorksheet("Sheet1");

        // ✅ 获取数据
        const data = this.hotInstance.getData();

        // ✅ 遍历数据并写入 Excel
        data.forEach((row, rowIndex) => {
          const excelRow = worksheet.addRow(row);

          // ✅ 遍历每个单元格并应用样式
          row.forEach((_, colIndex) => {
            const address = this.encodeCellAddress(rowIndex, colIndex);
            const cell = worksheet.getCell(address);
            const style = this.cellStyles[address];

            // ✅ 默认边框（所有单元格都有细边框）
            cell.border = {
              top: { style: 'thin', color: { argb: 'FF000000' } },
              left: { style: 'thin', color: { argb: 'FF000000' } },
              bottom: { style: 'thin', color: { argb: 'FF000000' } },
              right: { style: 'thin', color: { argb: 'FF000000' } }
            };

            // ✅ 应用自定义样式（覆盖默认样式）
            if (style) {
              // 背景色
              if (style.backgroundColor) {
                cell.fill = {
                  type: 'pattern',
                  pattern: 'solid',
                  fgColor: { argb: style.backgroundColor.replace("#", "") }
                };
              }

              // 字体样式
              if (style.fontColor || style.bold || style.italic || style.fontSize) {
                cell.font = {
                  color: { argb: style.fontColor?.replace("#", "") || 'FF000000' },
                  bold: style.bold || false,
                  italic: style.italic || false,
                  size: style.fontSize || 12
                };
              }

              // 对齐方式
              if (style.textAlign) {
                cell.alignment = { horizontal: style.textAlign };
              }

              // 自定义边框（可覆盖默认边框）
              if (style.border) {
                cell.border = {
                  top: { style: style.border.top?.style || 'thin', color: { argb: style.border.top?.color?.replace("#", "") || 'FF000000' } },
                  left: { style: style.border.left?.style || 'thin', color: { argb: style.border.left?.color?.replace("#", "") || 'FF000000' } },
                  bottom: { style: style.border.bottom?.style || 'thin', color: { argb: style.border.bottom?.color?.replace("#", "") || 'FF000000' } },
                  right: { style: style.border.right?.style || 'thin', color: { argb: style.border.right?.color?.replace("#", "") || 'FF000000' } }
                };
              }
            }
          });
        });

        // ✅ 合并单元格
        this.mergeCells.forEach(({ row, col, rowspan, colspan }) => {
          const start = this.encodeCellAddress(row, col);
          const end = this.encodeCellAddress(row + rowspan - 1, col + colspan - 1);
          worksheet.mergeCells(`${start}:${end}`);
        });

        // ✅ 导出 Excel 文件
        const buffer = await workbook.xlsx.writeBuffer();
        const blob = new Blob([buffer], { type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" });

        // 下载 Excel 文件
        const link = document.createElement("a");
        link.href = window.URL.createObjectURL(blob);
        link.download = "导出数据.xlsx";
        link.click();

        this.$modal.msgSuccess("Excel 导出成功！");
      },

    // ✅ 从服务器下载并显示 Excel
    async download(data) {
    const jsonString = JSON.stringify(data);
    const obj=JSON.parse(jsonString);
    this.selectedFile=JSON.parse(obj.id);
      if (!this.selectedFile) {
        alert("请选择文件");
        return;
      }
      try {
        const arrayBuffer = await this.downloadFile();
        await this.loadExcelData(arrayBuffer);
      } catch (error) {
        console.error("下载失败:", error);
        alert("下载失败");
      }
    },

     async getExcel() {
          this.loading = true;
          try {
            const response = await getExcelInfo(this.selectedFile);

            this.form = {
              userexcelId: response.data.userexcelId,
              userId: response.data.userId,
              excelId: response.data.excelId,
              exceldocDate: response.data.exceldocDate,
              exceldocContent: response.data.exceldocContent,
              exceldocRemark: response.data.exceldocRemark
            };
            // 如果没有保存的数据，则加载原始Excel
            const arrayBuffer = await this.downloadFile(this.form.excelId);
            const savedData = JSON.parse(this.form.exceldocContent);
            await this.loadExcelData(arrayBuffer,savedData);
          } catch (error) {
            console.error("加载 Excel 数据失败:", error);
            throw error;
          } finally {
            this.loading = false;
          }
        },

    async handleSave() {
    try {
         // 弹出确认对话框
         await this.$modal.confirm('确定要保存数据吗？')
            // 用户点击确定后执行保存
         this.form.exceldocContent = JSON.stringify(this.hotInstance.getData());
         const date = new Date();
         const year = date.getFullYear();
         const month = String(date.getMonth() + 1).padStart(2, '0');
         const day = String(date.getDate()).padStart(2, '0');

         this.form.exceldocDate = `${year}-${month}-${day}`;
         //console.log('this.$store.getters.user.userId',this.$store.getters.user.userId);
         this.form.userId =this.$store.state.user.id;
         if(this.form.userexcelId == null)
         {
           await addExcelData(this.form);
         }
         else{
           await updateExcelData(this.form);
         }
         this.$modal.msgSuccess('保存成功')
      } catch (error) {
         // 用户点击取消会进入catch，error不是真正的错误
         if (error !== 'cancel') {
           console.error('保存失败:', error)
           Message.error('保存失败: ' + (error.message || error))
         }
       }
    },


    async downloadFile(excelId) {

      const url = `${baseURL}/system/excel/download?id=${excelId}`;
      try {
        const response = await axios.get(url, {
          responseType: 'arraybuffer',
          headers: { 'Authorization': `Bearer ${getToken()}` }
        });
        return response.data;
      } catch (error) {
        throw new Error("文件下载失败");
      }
    },

    // ✅ 本地上传文件处理
    async handleFileUpload(event) {
      const file = event.target.files[0];
      if (!file) return;

      const reader = new FileReader();
      reader.readAsArrayBuffer(file);
      reader.onload = async (e) => {
        const arrayBuffer = e.target.result;
        await this.loadExcelData(arrayBuffer);
      };
    },

    // ✅ 解析 Excel 数据
    async loadExcelData(arrayBuffer, savedData) {
      const workbook = new ExcelJS.Workbook();
      await workbook.xlsx.load(arrayBuffer);

      let worksheet = workbook.getWorksheet(1);
      //console.log("原始 Excel 数据:", worksheet);
      //console.log("保存的数据:", savedData);

      // ✅ 合并原始数据和 savedData
      this.mergeDataToWorksheet(worksheet, savedData);

      // 提取数据、样式和合并信息
      const data = this.extractSheetData(worksheet);
      this.cellStyles = this.extractCellStyles(worksheet);
      this.mergeCells = this.extractMergeCells(worksheet);

      this.initHyperFormula();
      this.renderHandsontable(data);

      // ✅ 替换 #ERROR! 为 0.00
      this.$nextTick(() => {
        this.replaceErrorsWithDefault();
      });
    },

    mergeDataToWorksheet(worksheet, savedData) {
      if (!savedData || savedData.length === 0) {
        console.warn("没有保存的数据可合并！");
        return;
      }

      // 获取原有数据的行数和列数
      const maxRow = Math.max(worksheet.rowCount, savedData.length);
      const maxCol = savedData.reduce((max, row) => Math.max(max, row.length), worksheet.columnCount);

      // 遍历并合并数据
      for (let rowIndex = 0; rowIndex < maxRow; rowIndex++) {
        for (let colIndex = 0; colIndex < maxCol; colIndex++) {
          const cell = worksheet.getCell(rowIndex + 1, colIndex + 1);

          // 合并逻辑：
          // 1. 如果 savedData 中存在值，则使用 savedData
          // 2. 如果 savedData 为 null 且原有数据存在，则保持原有数据
          const savedValue = savedData[rowIndex]?.[colIndex];
          if (savedValue !== null && savedValue !== undefined) {
            cell.value = savedValue;  // 使用 savedData 中的值
          } else {
            // 如果 savedData 没有值，保留原有数据
            cell.value = cell.value || "";
          }
        }
      }

      console.log("✅ 已合并 savedData 和原有数据！");
    },


    writeSavedDataToWorksheet(worksheet, savedData) {
      if (!savedData || savedData.length === 0) {
        console.warn("没有保存的数据可写入！");
        return;
      }

      // 遍历 savedData 并写入 worksheet
      savedData.forEach((row, rowIndex) => {
        row.forEach((cellValue, colIndex) => {
          const cell = worksheet.getCell(rowIndex + 1, colIndex + 1);  // Excel 索引从 1 开始
          cell.value = cellValue;
        });
      })

      console.log("已将 savedData 写入 worksheet！");
    },


    // ✅ 替换 #ERROR! 为 0.00
        replaceErrorsWithDefault() {
          if (!this.hotInstance) return;

          const rowCount = this.hotInstance.countRows();
          const colCount = this.hotInstance.countCols();

          for (let row = 0; row < rowCount; row++) {
            for (let col = 0; col < colCount; col++) {
              const value = this.hotInstance.getDataAtCell(row, col);
              if (value === "#ERROR!") {
                this.hotInstance.setDataAtCell(row, col, "0.00");
              }
            }
          }
        },

    // ✅ 解析数据
    extractSheetData(worksheet) {
      const data = [];
      worksheet.eachRow({ includeEmpty: true }, (row) => {
        const rowData = [];
        row.eachCell({ includeEmpty: true }, (cell) => {
          let value = cell.value;

          // 处理公式和富文本
          if (cell.formula) {
            value = `=${cell.formula}`;
          } else if (value && typeof value === "object") {
            if (value.richText) {
              value = value.richText.map(rt => rt.text).join('');
            } else if (value.result !== undefined) {
              value = value.result;
            }
          }

          rowData.push(value || "");
        });
        data.push(rowData);
      });

      return data;
    },

    // ✅ 提取合并单元格信息
    extractMergeCells(worksheet) {
      const mergeCells = [];
      worksheet.model.merges.forEach((merge) => {
        const [start, end] = merge.split(":");
        const startCell = this.decodeCellAddress(start);
        const endCell = this.decodeCellAddress(end);

        mergeCells.push({
          row: startCell.row,
          col: startCell.col,
          rowspan: endCell.row - startCell.row + 1,
          colspan: endCell.col - startCell.col + 1
        });
      });
      return mergeCells;
    },

    // ✅ 提取单元格样式
    extractCellStyles(worksheet) {
      const styles = {};
      worksheet.eachRow({ includeEmpty: true }, (row, rowIndex) => {
        row.eachCell({ includeEmpty: true }, (cell, colIndex) => {
          const address = this.encodeCellAddress(rowIndex - 1, colIndex - 1);
          const style = cell.style;

          styles[address] = {
            backgroundColor: style.fill?.fgColor?.argb ? `#${style.fill.fgColor.argb.slice(2)}` : null,
            fontColor: style.font?.color?.argb ? `#${style.font.color.argb.slice(2)}` : null,
            bold: style.font?.bold || false,
            italic: style.font?.italic || false,
            fontSize: style.font?.size || 12,
            textAlign: style.alignment?.horizontal || "left",
            border: this.extractBorderStyle(style.border)
          };
        });
      });

      return styles;
    },

    // ✅ 提取边框样式
    extractBorderStyle(border) {
      if (!border) return null;
      let style = {};
      if (border.left) style.borderLeft = "1px solid #000";
      if (border.right) style.borderRight = "1px solid #000";
      if (border.top) style.borderTop = "1px solid #000";
      if (border.bottom) style.borderBottom = "1px solid #000";
      return style;
    },

    // ✅ 将 Excel 地址转换为 Handsontable 索引
    decodeCellAddress(address) {
      const match = address.match(/([A-Z]+)(\d+)/);
      if (!match) return { row: 0, col: 0 };

      const colLetters = match[1];
      const rowNumber = parseInt(match[2], 10) - 1;
      let colNumber = 0;

      for (let i = 0; i < colLetters.length; i++) {
        colNumber = colNumber * 26 + (colLetters.charCodeAt(i) - 64);
      }

      return { row: rowNumber, col: colNumber - 1 };
    },

    // ✅ 将行列索引转换为 Excel 地址
    encodeCellAddress(row, col) {
      let colLetters = "";
      col += 1;
      while (col > 0) {
        let remainder = (col - 1) % 26;
        colLetters = String.fromCharCode(65 + remainder) + colLetters;
        col = Math.floor((col - 1) / 26);
      }
      return `${colLetters}${row + 1}`;
    },

    // ✅ 初始化 HyperFormula
    initHyperFormula() {
      this.hyperformula = HyperFormula.buildEmpty({
        licenseKey: "internal-use-in-handsontable"
      });
    },

    // ✅ 渲染 Handsontable
    renderHandsontable(data) {
      if (this.hotInstance) {
        this.hotInstance.destroy();
      }

      this.hotInstance = new Handsontable(this.$refs.hotTable, {
        data,
        rowHeaders: true,
        colHeaders: true,
        mergeCells: this.mergeCells,
        formulas: { engine: this.hyperformula },
        cells: (row, col) => {
          return { renderer: this.customRenderer };
        }
      });
    },

    // ✅ 自定义渲染器
    customRenderer(instance, td, row, col, prop, value, cellProperties) {
          Handsontable.renderers.TextRenderer.apply(this, arguments);
          const address = this.encodeCellAddress(row, col);
          const style = this.cellStyles[address];

          if (style) {
            td.style.backgroundColor = style.backgroundColor || "";
            td.style.color = style.fontColor || "";
            td.style.fontWeight = style.bold ? "bold" : "";
            td.style.fontStyle = style.italic ? "italic" : "";
            td.style.textAlign = style.textAlign || "";
            Object.assign(td.style, style.border);
          }
        }
  }
};
</script>

<style>
.handsontable-container {
  width: 100%;
  height: 500px;
  overflow: auto;
}

.chat-app {
  display: flex;
    height: 100vh;
}

.left-side {
  width: 150px;
  border-right: 1px solid #ddd;
  padding: 10px;
  overflow-y: auto;
}

.right-side {
  flex: 1;
  display: flex;
  flex-direction: column;
}

</style>

