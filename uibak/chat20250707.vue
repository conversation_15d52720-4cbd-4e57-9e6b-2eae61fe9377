<template>
<div class="chat-app">
    <!-- 左侧树控件 -->

    <div class="left-side">
       <div class="chat-input">
        <button @click="newMessage">
                  <span>新建对话</span>
         </button>
       </div>
      <el-tree
        :data="treeData"
        :props="defaultProps"
        @node-click="handleNodeClick"
      ></el-tree>
    </div>

    <!-- 右侧聊天区域 -->
    <div class="right-side">
      <!-- 聊天头部 -->
      <div class="chat-header">
        <h1>{{this.chatLabel}}</h1>
      </div>

      <!-- 聊天消息区域 -->
      <div class="chat-messages">
        <MessageBubble
          v-for="(message, index) in messages"
          :key="index"
          :message="message"
        />
      </div>

      <!-- 输入框和发送按钮 -->
      <div class="chat-input">
        <input
          v-model="userInput"
          @keyup.enter="sendMessage"
          placeholder="输入你的消息..."
        />
        <button @click="sendMessage">
          <span>发送</span>
        </button>

      </div>
    </div>
  </div>
</template>

<script>
import MessageBubble from '@/components/MessageBubble';
import { parseTime } from '@/utils/ruoyi';
import { listChathistory, getChathistory, delChathistory, addChathistory, updateChathistory, getTree } from "@/api/system/chathistory";
import { getDicts } from "@/api/system/dict/data"; // ⭐ 新增

export default {
  components: {
    MessageBubble,
  },
  data() {
    return {
      // 树控件数据
      treeData: [],
      defaultProps: {
        children: 'children',
        label: 'label'
      },
      userInput: '', // 用户输入的消息
      messages: [], // 聊天记录

      // 联系人列表
      contactList: [],
      contactListTotal: 0,
      contactListLoading: false,

      // 消息记录
      msgList: [],
      msgListTotal: 0,
      msgListLoading: false,
      inputVal: '',
      search: '',
      contactUserId: null,
      userId: null,
      contactQueryParams: {
        pageSize: 10,
        pageNum: 1
      },
      form : {
        chatId: null,
        userId: null,
        chatTile: null,
        chatContent: null,
        chatTime: null,
        chatRemark: null
      },
      currentContact: {},

      // ⭐ 新增
      chatDict: [],      // 存 sys_chat 字典数据
      chatLabel: ''      // 根据 chatId 获取到的标签
    };
  },

  mounted() {
    window.addEventListener("onmessageWS", (event) => {
      const data = event.detail.data;

      const showdown = require('showdown');
      const converter = new showdown.Converter();

      var temp = this.messages[this.messages.length-1].text.replace('请稍后.....','');
      temp = temp.replace("<p>","").replace("</p>","");
      if(temp.length < this.messages[this.messages.length-1].text.length) {
        this.messages.pop();
      }

      const htmlText = converter.makeHtml(data);
      this.messages.push({ text: htmlText, sender: 'ai', index:this.messages.length });
      var eleId = this.messages.length-1;
      const preview = document.getElementById('message'+eleId);

      let i = 0;
      var singlemessage = setInterval(() => {
        if(i==0) singlemessage='';
        if (i < htmlText.length) {
          singlemessage += htmlText.charAt(i);
          preview.innerHTML = singlemessage;
          i++;
        } else {
          clearInterval(singlemessage);
        }
      }, 100);
    });
  },

  beforeDestroy() {
    this.form.userId = this.userId;
    this.form.chatTile = JSON.stringify(this.messages[0]);
    const jsonString = JSON.stringify(this.messages);
    this.form.chatContent = jsonString;
    this.form.chatTime = parseTime(new Date());
    this.form.chatRemark = '';
    if(this.messages.length > 1) {
      addChathistory(this.form).then(() => {});
    }
  },

  created() {
    //this.userId = this.$store.state.user.id;
    const chatId = this.$route.params.id;
    this.userId =chatId;
    this.form.chatId=this.$route.params.id;
    this.makeTree();

    // ⭐ 新增
    this.loadSysChatDict();

    // 如果有 chatId 可以直接赋值，例如：
    // this.form.chatId = 1;
  },

  methods: {
    makeTree() {
      getTree(this.userId).then(response => {
        this.treeData = response;
      });
    },

    // ⭐ 新增 获取 sys_chat 字典
    loadSysChatDict() {
      getDicts('sys_chat').then(res => {
        this.chatDict = res.data || [];
        console.log('sys_chat 字典数据：', this.chatDict);

        if (this.form.chatId) {
          this.chatLabel = this.getChatLabelById(this.form.chatId);
          console.log(`chatId=${this.form.dictLabel} 的标签=`, this.chatLabel);
        }
        else{
        this.chatLabel ='学习机器人112';
        }
      });
    },

    // ⭐ 新增 根据 id 获取 label
    getChatLabelById(id) {
       const item = this.chatDict.find(d => String(d.dictLabel) === String(id));
        return item ? item.dictValue : '学习机器人234';
    },

    handleNodeClick(data) {
      const jsonString = JSON.stringify(data);
      const obj = JSON.parse(jsonString);
      this.messages = JSON.parse(obj.label);
      for(var i=0; i<this.messages.length; i++) {
        const preview = document.getElementById('message'+i);
        preview.innerHTML = this.messages[i];
      }
    },

    newMessage() {
      this.form.userId = this.userId;
      this.form.chatTile = JSON.stringify(this.messages[0]);
      const jsonString = JSON.stringify(this.messages);
      this.form.chatContent = jsonString;
      this.form.chatTime = parseTime(new Date());
      this.form.chatRemark = '';
      if(this.messages.length > 1) {
        addChathistory(this.form).then(() => {});
      }
      window.location.reload();
    },

    sendMessage() {
      if (this.userInput.trim() === '') return;

      this.messages.push({ text: this.userInput, sender: 'user' });

      setTimeout(() => {
        // 模拟回复
      }, 1000);

      const singlemessage = {
        userId: this.userId,
        content: this.userInput
      }
      this.msgList.push({
        ...singlemessage,
        id: this.msgList.length + 1,
        createTime: parseTime(new Date())
      })
      const msg = {
        sendUserId: this.userId,
        sendUserName: this.$store.state.user.name,
        userId: this.userId,
        type: "chat",
        detail: this.userInput
      }
      this.$websocket.sendWebsocket(JSON.stringify(msg));
      this.userInput = '';
    },

    fleshLastMsg() {
      // … 你的原 fleshLastMsg …
    },

    subscribeMessage(res) {
      if (res) {
        const { sendUserId, sendUserName, userId, type, detail } = res.detail.data;
        const message = {
          id: 1,
          contactId: userId,
          userId: sendUserId,
          content: detail,
          createTime: parseTime(new Date())
        }
        this.msgList.push(message);
      }
    }
  }
};
</script>

<style>
/* 全局样式 */
body {
  font-family: Arial, sans-serif;
  background-color: #f7f7f7;
  margin: 0;
  padding: 0;
}

.chat-app {
  display: flex;
    height: 100vh;
}

.left-side {
  width: 250px;
  border-right: 1px solid #ddd;
  padding: 10px;
  overflow-y: auto;
}

.right-side {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.chat-header {
  background-color: #202123;
  color: white;
  padding: 20px;
  text-align: center;
}

.chat-messages {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
  background-color: #f9f9f9;
}

.chat-input {
  display: flex;
  padding: 10px;
  background-color: #fff;
  border-top: 1px solid #ddd;
}

.chat-input input {
  flex: 1;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 5px;
  margin-right: 10px;
  font-size: 16px;
}

.chat-input button {
  padding: 10px 20px;
  background-color: #202123;
  color: white;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  font-size: 16px;
}

.chat-input button:hover {
  background-color: #333;
}
</style>
