<template>
  <div class="chat-app">
    <!-- 左侧树和新建按钮 -->
    <div class="left-side">
      <div class="chat-input">
        <button @click="newMessage">
          <span>新建对话</span>
        </button>
      </div>

      <el-tree
        :data="treeData"
        :props="defaultProps"
        @node-click="handleNodeClick"
      ></el-tree>
    </div>

    <!-- 右侧聊天区 -->
    <div class="right-side">
      <div class="chat-header">
        <h1>{{ chatLabel || '聊天机器人' }}</h1>
      </div>

      <div class="chat-messages">
        <MessageBubble
          v-for="(msg, i) in messages"
          :key="i"
          :message="msg"
        />
      </div>

      <div class="chat-input">
        <input v-model="userInput" @keyup.enter="sendMessage" placeholder="输入你的消息..." />
        <button @click="sendMessage">
          <span>发送</span>
        </button>
      </div>
    </div>
  </div>
</template>

<script>
import MessageBubble from '@/components/MessageBubble';
import { parseTime } from '@/utils/ruoyi';
import { getTree, addChathistory } from '@/api/system/chathistory';
import { getDicts } from '@/api/system/dict/data';
import store from '@/store';
import { completion } from "@/api/system/introductionjob";

export default {
  components: { MessageBubble },
  data() {
    return {
      userId: null,
      treeData: [],
      defaultProps: { children: 'children', label: 'label' },
      messages: [],
      userInput: '',
      chatLabel: '',
      chatDict: [],
      form: {
        chatId: null,
        userId: null,
        chatTile: null,
        chatContent: null,
        chatTime: null,
        chatRemark: null
      }
    };
  },
  created() {
    this.userId = this.$route.params.id;
    store.state.user.id = this.userId;
    this.form.userId = this.userId;
    this.form.chatId = this.userId;

    this.loadTree();
    this.loadSysChatDict();

    this.$websocket.initWebSocket();

    window.addEventListener("onmessageWS", this.onMessage);
  },
  beforeDestroy() {
    this.saveHistory();
    window.removeEventListener("onmessageWS", this.onMessage);
  },
  methods: {
    loadTree() {
      getTree(this.userId).then(res => {
        this.treeData = res;
      });
    },
    loadSysChatDict() {
      getDicts('sys_chat').then(res => {
        this.chatDict = res.data || [];
        this.chatLabel = this.getChatLabelById(this.form.chatId);
      });
    },
    getChatLabelById(id) {
      const item = this.chatDict.find(d => String(d.dictLabel) === String(id));
      return item ? item.dictValue : `用户-${id}`;
    },
    handleNodeClick(node) {
      this.messages = JSON.parse(node.label);
    },

    /** 点击【发送按钮】 */
    sendMessage() {
      if (!this.userInput.trim()) return;

      this.messages.push({ text: this.userInput, sender: 'user' });

      const msg = {
        sendUserId: this.userId,
        sendUserName: store.state.user.name || '用户',
        userId: this.userId,
        type: 'chat',
        detail: this.userInput
      };

      this.$websocket.sendWebsocket(JSON.stringify(msg));

      //completion(JSON.stringify(msg)).then(response => {
      //                          console.log('openAI返回'+response);

      //                        });

      this.userInput = '';
    },

    /** 接收 websocket 消息 */
    onMessage(event) {
      const data = event.detail.data;
      this.messages.push({ text: data, sender: 'ai' });
    },

    /** 保存当前历史记录 */
    saveHistory() {
      if (!this.messages.length) return;

      this.form.chatTile = JSON.stringify(this.messages[0]);
      this.form.chatContent = JSON.stringify(this.messages);
      this.form.chatTime = parseTime(new Date());
      this.form.chatRemark = '';
      addChathistory(this.form).then(() => {
        this.loadTree();
      });
    },

    /** 点击【新建按钮】 */
    newMessage() {
      this.saveHistory();
      this.messages = [];
      this.userInput = '';
    }
  }
};
</script>

<style scoped>
.chat-app {
  display: flex;
  height: 100vh;
}
.left-side {
  width: 250px;
  border-right: 1px solid #ddd;
  padding: 10px;
  overflow-y: auto;
}
.right-side {
  flex: 1;
  display: flex;
  flex-direction: column;
}
.chat-header {
  background-color: #202123;
  color: #fff;
  padding: 15px;
  text-align: center;
}
.chat-messages {
  flex: 1;
  overflow-y: auto;
  padding: 10px;
  background-color: #f9f9f9;
}
.chat-input {
  display: flex;
  border-top: 1px solid #ddd;
  padding: 10px;
  background-color: #fff;
}
.chat-input input {
  flex: 1;
  margin-right: 10px;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 5px;
  font-size: 16px;
}
.chat-input button {
  padding: 10px 20px;
  background-color: #202123;
  color: #fff;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  font-size: 16px;
}
.chat-input button:hover {
  background-color: #333;
}
</style>
