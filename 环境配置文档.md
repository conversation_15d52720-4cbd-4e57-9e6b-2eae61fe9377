# 审计实训教学系统环境配置文档

## 📋 项目概述

**项目名称**: 审计实训教学系统  
**基础框架**: RuoYi 3.8.9  
**架构模式**: Vue + Java 前后端分离  
**项目版本**: 3.8.9  

## 🏗️ 系统架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│    前端层       │    │    后端层       │    │    数据层       │
│                 │    │                 │    │                 │
│ Vue 2.6.12      │────│ Spring Boot     │────│ MySQL 5.7+      │
│ Element UI      │    │ 2.5.15          │    │ auditTrain DB   │
│ Handsontable    │    │ Spring Security │    │                 │
│ Excel组件群     │    │ MyBatis         │    │ Redis 6.0+      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 🔧 基础环境要求

### 1. Java开发环境
- **JDK版本**: JDK 1.8 及以上
- **构建工具**: Apache Maven 3.6+
- **IDE推荐**: IntelliJ IDEA 或 Eclipse

### 2. Node.js前端环境
- **Node.js版本**: >= 8.9
- **NPM版本**: >= 3.0.0
- **推荐使用**: Node.js 14+ 或 16+

### 3. 数据库环境
- **数据库**: MySQL 5.7+ 或 MySQL 8.0+
- **数据库名**: `auditTrain`
- **初始化脚本**: `sql/ry_20240629.sql`

### 4. 缓存数据库
- **Redis**: 6.0+ 
- **默认端口**: 6379
- **无密码配置**（开发环境）

## 🌐 网络和端口配置

### 后端服务
- **应用端口**: 8080
- **Swagger文档**: http://localhost:8080/swagger-ui/
- **Druid监控**: http://localhost:8080/druid/
  - 用户名: ruoyi
  - 密码: 123456

### 前端服务
- **开发端口**: 80
- **开发代理**: 指向 http://************:8080（生产服务器）

### WebSocket服务
- **WebSocket地址**: ws://localhost:8080/websocket/

## 📁 文件存储配置

### 文件上传路径
- **Windows**: `D:/ruoyi/uploadPath` 或 `C:/ruoyi/uploadPath`
- **Linux**: `/home/<USER>/uploadPath`
- **票据图片**: `C:/apache-tomcat-9.0.83/webapps/ROOT/static/bill`

## ⚙️ 关键配置参数

### 数据库连接配置
```yaml
spring:
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driverClassName: com.mysql.cj.jdbc.Driver
    url: **************************************?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=true&serverTimezone=GMT%2B8
    username: root
    password: 123456
```

### Redis配置
```yaml
spring:
  redis:
    host: localhost
    port: 6379
    database: 0
    password: # 无密码
    timeout: 10s
```

## 🔐 安全配置

### JWT Token配置
- **密钥**: abcdefghijklmnopqrstuvwxyz
- **有效期**: 30分钟
- **请求头**: Authorization

### 系统默认账户
- **管理员**: admin/admin123
- **普通用户**: ry/admin123
- **Druid监控**: ruoyi/123456

## 🚀 第三方服务集成

### OpenAI API配置
- **API Key**: sk-3tjm9edecg0pu2jqfobqn22etcumqogb0p5ltmcl7n7qmtj8

### 腾讯云SDK
- 已集成腾讯云SDK（最新版本）

## 📦 关键依赖版本

### 后端核心依赖
| 组件 | 版本 | 说明 |
|------|------|------|
| Spring Boot | 2.5.15 | 核心框架 |
| Spring Security | 5.7.12 | 安全框架 |
| Spring Framework | 5.3.39 | 基础框架 |
| MySQL Connector | 自动管理 | 数据库驱动 |
| Druid | 1.2.23 | 连接池 |
| MyBatis | 集成版本 | ORM框架 |
| Redis | Lettuce客户端 | 缓存客户端 |

### 前端核心依赖
| 组件 | 版本 | 说明 |
|------|------|------|
| Vue | 2.6.12 | 前端框架 |
| Element UI | 2.15.14 | UI组件库 |
| Vue Router | 3.4.9 | 路由管理 |
| Vuex | 3.6.0 | 状态管理 |
| Axios | 0.28.1 | HTTP客户端 |

### 特殊组件
| 组件 | 版本 | 用途 |
|------|------|------|
| Handsontable | 15.1.0 | 表格组件 |
| Luckysheet | 2.1.13 | 在线表格 |
| ExcelJS | 4.4.0 | Excel处理 |
| ECharts | 5.4.0 | 图表组件 |

## 💻 开发工具推荐

### IDE和编辑器
- **后端**: IntelliJ IDEA Ultimate
- **前端**: VS Code
- **数据库**: Navicat 或 DataGrip

### 版本控制
- **Git**: 2.30+
- **代码仓库**: 支持GitLab/GitHub

## 🔧 Maven仓库配置

项目已配置阿里云Maven镜像：
```xml
<repository>
  <id>public</id>
  <name>aliyun nexus</name>
  <url>https://maven.aliyun.com/repository/public</url>
  <releases>
    <enabled>true</enabled>
  </releases>
</repository>
```

## 📋 部署检查清单

### 步骤1: 环境准备
- [ ] 安装JDK 1.8+
- [ ] 安装Maven 3.6+
- [ ] 安装Node.js 8.9+
- [ ] 安装MySQL 5.7+
- [ ] 安装Redis 6.0+

### 步骤2: 数据库配置
- [ ] 创建数据库 `auditTrain`
- [ ] 执行初始化脚本 `sql/ry_20240629.sql`
- [ ] 验证数据表创建完成

### 步骤3: 后端配置
- [ ] 修改 `application.yml` 数据库连接信息
- [ ] 修改 `application-druid.yml` 连接池配置
- [ ] 配置Redis连接参数
- [ ] 设置文件上传路径

### 步骤4: 前端配置
- [ ] 安装前端依赖 `npm install`
- [ ] 配置 `.env.development` 开发环境变量
- [ ] 配置 `.env.production` 生产环境变量
- [ ] 修改 `vue.config.js` 代理设置

### 步骤5: 服务启动
- [ ] 启动Redis服务
- [ ] 启动MySQL服务
- [ ] 启动后端服务 `mvn spring-boot:run`
- [ ] 启动前端服务 `npm run dev`

### 步骤6: 验证部署
- [ ] 访问前端页面 http://localhost:80
- [ ] 访问后端API http://localhost:8080
- [ ] 访问Swagger文档 http://localhost:8080/swagger-ui/
- [ ] 测试用户登录功能
- [ ] 验证WebSocket连接

## 🚨 常见问题

### 数据库连接问题
- 确保MySQL服务正在运行
- 检查数据库用户权限
- 验证时区设置 `serverTimezone=GMT%2B8`

### Redis连接问题
- 确保Redis服务启动
- 检查端口6379是否被占用
- 验证防火墙设置

### 前端构建问题
- 清除node_modules重新安装
- 检查Node.js版本兼容性
- 验证npm镜像源设置

### 文件上传问题
- 检查上传路径权限
- 验证磁盘空间充足
- 确认路径配置正确

## 📞 技术支持

- **项目文档**: 查看项目根目录README.md
- **配置文件**: 重点关注application.yml和vue.config.js
- **日志文件**: 检查后端日志和浏览器控制台
- **端口占用**: 使用netstat或lsof检查端口状态

## 🐳 OrbStack + Docker 容器化部署方案

### 部署架构设计

```
┌─────────────────────┐    ┌─────────────────────┐
│   本地开发环境      │    │  OrbStack容器环境   │
│                     │    │                     │
│  ✅ 本地Java JDK    │    │  🐳 MySQL 5.7       │
│  ✅ Maven构建       │────│  🐳 Redis 6.0       │
│  ✅ 后端服务:8080   │    │  🐳 前端Nginx:80    │
└─────────────────────┘    └─────────────────────┘
```

### Docker Compose 配置

创建 `docker-compose.yml` 文件：

```yaml
version: '3.8'

services:
  # MySQL数据库服务
  mysql:
    image: mysql:5.7
    container_name: audit-mysql
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: 123456
      MYSQL_DATABASE: auditTrain
      MYSQL_CHARACTER_SET_SERVER: utf8mb4
      MYSQL_COLLATION_SERVER: utf8mb4_unicode_ci
      TZ: Asia/Shanghai
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./sql:/docker-entrypoint-initdb.d
      - ./mysql/conf:/etc/mysql/conf.d
    command: --default-authentication-plugin=mysql_native_password
    networks:
      - audit-network

  # Redis缓存服务
  redis:
    image: redis:6.0-alpine
    container_name: audit-redis
    restart: always
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    networks:
      - audit-network

  # 前端Nginx服务
  frontend:
    build:
      context: ./ruoyi-ui
      dockerfile: Dockerfile
    container_name: audit-frontend
    restart: always
    ports:
      - "80:80"
    depends_on:
      - mysql
      - redis
    networks:
      - audit-network

volumes:
  mysql_data:
  redis_data:

networks:
  audit-network:
    driver: bridge
```

### 前端 Dockerfile

在 `ruoyi-ui/` 目录下创建 `Dockerfile`：

```dockerfile
# 构建阶段
FROM node:16-alpine as build-stage
WORKDIR /app
COPY package*.json ./
RUN npm install --registry=https://registry.npmmirror.com
COPY . .
RUN npm run build:prod

# 生产阶段
FROM nginx:alpine as production-stage
COPY --from=build-stage /app/dist /usr/share/nginx/html
COPY nginx.conf /etc/nginx/nginx.conf
EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
```

### Nginx 配置

在 `ruoyi-ui/` 目录下创建 `nginx.conf`：

```nginx
worker_processes 1;

events {
    worker_connections 1024;
}

http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;
    
    sendfile        on;
    keepalive_timeout  65;
    
    # gzip压缩
    gzip on;
    gzip_min_length 1k;
    gzip_comp_level 9;
    gzip_types text/plain text/css text/javascript application/json application/javascript application/xml+rss application/atom+xml image/svg+xml;
    
    server {
        listen 80;
        server_name localhost;
        
        location / {
            root /usr/share/nginx/html;
            try_files $uri $uri/ /index.html;
            index index.html index.htm;
        }
        
        # API代理
        location /prod-api/ {
            proxy_pass http://host.docker.internal:8080/;
            proxy_set_header Host $http_host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header REMOTE-HOST $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        }
        
        error_page 500 502 503 504 /50x.html;
        location = /50x.html {
            root /usr/share/nginx/html;
        }
    }
}
```

### MySQL 配置优化

创建 `mysql/conf/my.cnf`：

```ini
[mysqld]
default-storage-engine=INNODB
character-set-server=utf8mb4
collation-server=utf8mb4_unicode_ci
init_connect='SET NAMES utf8mb4'

# 性能优化
max_connections=1000
innodb_buffer_pool_size=256M
innodb_log_file_size=128M
innodb_log_buffer_size=16M
key_buffer_size=64M

# 时区设置
default-time-zone='+8:00'

[mysql]
default-character-set=utf8mb4

[client]
default-character-set=utf8mb4
```

### 🚀 容器化部署步骤

#### 1. 准备工作
```bash
# 确保OrbStack正在运行
# 确保本地已安装Java和Maven

# 克隆或拷贝项目代码到本地
cd /path/to/AuditTrain
```

#### 2. 启动容器服务
```bash
# 启动MySQL和Redis容器
docker-compose up -d mysql redis

# 等待MySQL完全启动（约30秒）
docker-compose logs -f mysql

# 验证服务状态
docker-compose ps
```

#### 3. 数据库初始化
```bash
# 检查数据库是否正确初始化
docker exec -it audit-mysql mysql -u root -p123456 -e "USE auditTrain; SHOW TABLES;"

# 如果初始化失败，手动执行
docker exec -i audit-mysql mysql -u root -p123456 auditTrain < sql/ry_20240629.sql
```

#### 4. 后端服务启动
```bash
# 修改application-druid.yml中的数据库连接（如果需要）
# url: **************************************...

# 编译并启动后端服务
mvn clean compile
mvn spring-boot:run
```

#### 5. 前端容器构建（可选）
```bash
# 如果需要容器化前端
docker-compose up -d frontend

# 或者直接本地运行前端
cd ruoyi-ui
npm install
npm run dev
```

### 🔧 开发环境配置调整

#### 后端配置调整
无需修改，因为MySQL和Redis都映射到了本地端口（3306和6379）

#### 前端配置调整
修改 `ruoyi-ui/.env.development`：

```env
# 页面标题
VUE_APP_TITLE = 审计实训教学系统

# 开发环境配置
ENV = 'development'

# 后端API地址（指向本地Java服务）
VUE_APP_BASE_API = 'http://localhost:8080'

# WebSocket服务地址
VUE_APP_SOCKET_SERVER = 'ws://localhost:8080/websocket/'

# 路由懒加载
VUE_CLI_BABEL_TRANSPILE_MODULES = true
```

### 📊 服务管理命令

```bash
# 启动所有服务
docker-compose up -d

# 查看运行状态
docker-compose ps

# 查看日志
docker-compose logs -f [service_name]

# 停止服务
docker-compose down

# 重启服务
docker-compose restart [service_name]

# 清理数据（慎用）
docker-compose down -v
```

### 🎯 优势总结

1. **简化部署**: MySQL和Redis通过容器一键启动
2. **环境隔离**: 数据库和缓存与本地环境隔离
3. **保持灵活**: Java后端仍在本地运行，便于调试
4. **数据持久**: 数据卷确保数据不丢失
5. **一致性**: 所有开发者使用相同的数据库和Redis版本

### 🚨 注意事项

- 确保OrbStack正在运行
- 首次启动MySQL需要等待初始化完成
- 如遇到端口冲突，修改docker-compose.yml中的端口映射
- 数据库连接失败时，检查容器状态和网络连接

---

**文档生成时间**: 2025-01-29
**适用版本**: RuoYi 3.8.9
**维护状态**: 持续更新
**容器化方案**: OrbStack + Docker Compose