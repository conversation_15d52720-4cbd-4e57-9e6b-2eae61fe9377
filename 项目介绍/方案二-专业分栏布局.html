<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>方案二：专业分栏布局</title>
    <link rel="stylesheet" href="https://unpkg.com/element-ui/lib/theme-chalk/index.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Helvetica Neue', Arial, sans-serif;
            background: #f8f9fa;
            color: #333;
        }

        .professional-layout {
            min-height: 100vh;
            background: linear-gradient(to right, #e3f2fd 0%, #f8f9fa 50%, #fff 100%);
        }

        .layout-container {
            display: flex;
            max-width: 1400px;
            margin: 0 auto;
            min-height: 100vh;
        }

        .info-sidebar {
            width: 400px;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: white;
            padding: 40px 30px;
            position: relative;
        }

        .info-sidebar::before {
            content: '';
            position: absolute;
            top: 0;
            right: -20px;
            width: 40px;
            height: 100%;
            background: linear-gradient(135deg, #2a5298 0%, transparent 100%);
            clip-path: polygon(0 0, 50% 0, 0 100%);
        }

        .project-header {
            text-align: center;
            margin-bottom: 40px;
        }

        .project-icon {
            width: 80px;
            height: 80px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            backdrop-filter: blur(10px);
        }

        .project-icon i {
            font-size: 36px;
            color: white;
        }

        .project-name {
            font-size: 1.8rem;
            font-weight: 700;
            margin-bottom: 20px;
            line-height: 1.3;
        }

        .project-tags {
            display: flex;
            gap: 10px;
            justify-content: center;
            flex-wrap: wrap;
        }

        .tag {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .info-section {
            margin-bottom: 40px;
        }

        .info-section h3 {
            font-size: 1.2rem;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            border-bottom: 2px solid rgba(255, 255, 255, 0.3);
            padding-bottom: 10px;
        }

        .info-section h3 i {
            margin-right: 10px;
            font-size: 18px;
        }

        .description {
            line-height: 1.8;
            font-size: 14px;
            opacity: 0.9;
            text-align: justify;
        }

        .action-btn {
            width: 100%;
            background: rgba(255, 255, 255, 0.2);
            border: 2px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 15px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .action-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }

        .content-main {
            flex: 1;
            padding: 40px;
            background: white;
        }

        .section-header {
            margin-bottom: 30px;
        }

        .section-header h2 {
            font-size: 1.8rem;
            color: #1e3c72;
            display: flex;
            align-items: center;
            margin-bottom: 10px;
        }

        .section-header h2 i {
            margin-right: 12px;
            font-size: 24px;
        }

        .section-divider {
            height: 4px;
            background: linear-gradient(to right, #1e3c72, #2a5298, transparent);
            border-radius: 2px;
            width: 100px;
        }

        .single-image-gallery {
            width: 100%;
        }

        .main-image-container {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            transition: all 0.3s ease;
            border: 1px solid #e9ecef;
        }

        .main-image-container:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
        }

        .main-image {
            position: relative;
            height: 400px;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 50%, #4a90e2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            cursor: pointer;
            overflow: hidden;
        }

        .image-content {
            text-align: center;
            z-index: 2;
        }

        .image-title {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 10px;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        .image-subtitle {
            font-size: 1.1rem;
            opacity: 0.9;
            margin-bottom: 30px;
        }

        .image-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 15px;
            max-width: 500px;
            margin: 0 auto;
        }

        .mini-section {
            background: rgba(255, 255, 255, 0.2);
            padding: 12px 8px;
            border-radius: 8px;
            font-size: 12px;
            font-weight: 500;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            transition: all 0.3s ease;
        }

        .mini-section:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: scale(1.05);
        }

        .image-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.7);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            opacity: 0;
            transition: opacity 0.3s ease;
            z-index: 3;
        }

        .main-image:hover .image-overlay {
            opacity: 1;
        }

        .image-overlay i {
            font-size: 48px;
            margin-bottom: 10px;
        }

        .image-overlay span {
            font-size: 16px;
            font-weight: 600;
        }

        .image-description {
            padding: 25px;
        }

        .image-description h4 {
            color: #1e3c72;
            font-size: 1.2rem;
            margin-bottom: 12px;
            font-weight: 600;
        }

        .image-description p {
            color: #666;
            line-height: 1.6;
            font-size: 14px;
        }

        /* 项目截图展示样式 */
        .section-header {
            margin-bottom: 30px;
        }

        .section-header h2 {
            color: #2c3e50;
            font-size: 24px;
            font-weight: 600;
            margin: 0 0 15px 0;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .section-header h2 i {
            color: #3498db;
            font-size: 28px;
        }

        .section-divider {
            height: 3px;
            background: linear-gradient(90deg, #3498db, #2980b9);
            border-radius: 2px;
            width: 60px;
        }

        .main-screenshot-container {
            width: 100%;
            margin-top: 20px;
        }

        .screenshot-display {
            background: white;
            border-radius: 16px;
            overflow: hidden;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            border: 1px solid #e9ecef;
            min-height: 400px;
        }

        .screenshot-display:hover {
            transform: translateY(-3px);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
        }

        .screenshot-placeholder {
            height: 400px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }

        .screenshot-placeholder::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.3);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .screenshot-placeholder:hover::before {
            opacity: 1;
        }

        .placeholder-content {
            text-align: center;
            color: white;
            z-index: 2;
            position: relative;
        }

        .placeholder-content i {
            font-size: 64px;
            margin-bottom: 20px;
            opacity: 0.9;
        }

        .placeholder-content h3 {
            font-size: 1.8rem;
            margin-bottom: 12px;
            font-weight: 600;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        .placeholder-content p {
            font-size: 1rem;
            opacity: 0.8;
            margin: 0;
        }


        @media (max-width: 1024px) {
            .layout-container {
                flex-direction: column;
            }
            
            .info-sidebar {
                width: 100%;
                padding: 30px 20px;
            }
            
            .info-sidebar::before {
                display: none;
            }
            
            .content-main {
                padding: 30px 20px;
            }
        }

        @media (max-width: 768px) {
            .screenshot-placeholder {
                height: 300px;
            }

            .placeholder-content i {
                font-size: 48px;
                margin-bottom: 15px;
            }

            .placeholder-content h3 {
                font-size: 1.4rem;
                margin-bottom: 10px;
            }

            .placeholder-content p {
                font-size: 0.9rem;
            }

            .image-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 10px;
            }

            .mini-section {
                font-size: 11px;
                padding: 10px 6px;
            }

            .image-title {
                font-size: 1.5rem;
            }

            .image-subtitle {
                font-size: 1rem;
            }

            .main-image {
                height: 350px;
            }

            .project-name {
                font-size: 1.5rem;
            }
        }

        @media (max-width: 480px) {
            .image-grid {
                grid-template-columns: 1fr;
                gap: 8px;
            }

            .main-image {
                height: 300px;
            }

            .image-title {
                font-size: 1.3rem;
            }

            .screenshot-placeholder {
                height: 250px;
            }

            .placeholder-content i {
                font-size: 40px;
                margin-bottom: 12px;
            }

            .placeholder-content h3 {
                font-size: 1.2rem;
                margin-bottom: 8px;
            }

            .placeholder-content p {
                font-size: 0.8rem;
            }
        }
    </style>
</head>
<body>
    <div class="professional-layout">
        <div class="layout-container">
            <!-- 左侧信息栏 -->
            <div class="info-sidebar">
                <div class="project-header">
                    <div class="project-icon">
                        <i class="el-icon-office-building"></i>
                    </div>
                    <h1 class="project-name">天府新区会计师事务所审计项目</h1>
                    <div class="project-tags">
                        <span class="tag">审计项目</span>
                        <span class="tag">进行中</span>
                        <span class="tag">重要</span>
                    </div>
                </div>

                <div class="info-section">
                    <h3><i class="el-icon-document-copy"></i> 项目描述</h3>
                    <p class="description">
                        天府新区会计师事务所审计项目是针对中小企业财务状况进行全面审计的综合性项目。本项目涉及企业财务报表审计、内部控制评价、风险评估等多个方面，旨在为企业提供专业的财务咨询服务，确保财务信息的真实性和准确性。
                    </p>
                </div>

                <div class="info-section">
                    <h3><i class="el-icon-user"></i> 项目团队</h3>
                    <p class="description">
                        项目组由资深注册会计师领导，配备专业的审计团队，运用先进的审计技术和方法，为客户提供高质量的审计服务。
                    </p>
                </div>

                <div class="action-section">
                    <button class="action-btn">
                        <i class="el-icon-video-play"></i> 观看介绍视频
                    </button>
                </div>
            </div>

            <!-- 右侧内容区 -->
            <div class="content-main">
                <div class="section-header">
                    <h2><i class="el-icon-picture-outline"></i> 项目截图展示</h2>
                    <div class="section-divider"></div>
                </div>

                <div class="main-screenshot-container">
                    <div class="screenshot-display">
                        <div class="screenshot-placeholder">
                            <div class="placeholder-content">
                                <i class="el-icon-picture-outline"></i>
                                <h3>项目截图展示</h3>
                                <p>点击此处查看项目详细截图</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 为大图片展示区域添加点击效果
        document.querySelector('.screenshot-placeholder').addEventListener('click', function() {
            alert('查看项目详细截图');
        });

        // 为每个小模块添加点击效果
        document.querySelectorAll('.mini-section').forEach(item => {
            item.addEventListener('click', function(e) {
                e.stopPropagation();
                alert('查看详情：' + this.textContent);
            });
        });

        document.querySelector('.action-btn').addEventListener('click', function() {
            alert('跳转到视频播放页面');
        });
    </script>
</body>
</html>
